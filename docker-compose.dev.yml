# Development overrides for docker-compose.yml
# Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

services:
  # Development overrides for backend
  backend:
    build:
      target: development
    environment:
      NODE_ENV: development
      DEBUG: tenxcfo:*
      LOG_LEVEL: debug
    volumes:
      - ./backend:/app
      - /app/node_modules
    command: npm run dev
    stdin_open: true
    tty: true

  # Development overrides for frontend
  frontend:
    build:
      target: development
    environment:
      NODE_ENV: development
      NEXT_TELEMETRY_DISABLED: 1
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    command: npm run dev
    stdin_open: true
    tty: true

  # Development database with exposed ports and debug logging
  postgres:
    environment:
      POSTGRES_DB: tenxcfo_dev
      POSTGRES_USER: tenxcfo_dev
      POSTGRES_PASSWORD: tenxcfo_dev_password
    command: postgres -c log_statement=all -c log_destination=stderr -c log_min_messages=info

  # Development Redis with debug logging
  redis:
    command: redis-server --loglevel verbose

#!/bin/bash

# TenxCFO Development Setup Script
# This script sets up the development environment for the TenxCFO application

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo ""
    echo "=================================="
    echo "$1"
    echo "=================================="
    echo ""
}

# Check if Docker is installed and running
check_docker() {
    print_status "Checking Docker installation..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first:"
        echo "  https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    print_success "Docker is installed and running"
}

# Check if Docker Compose is available
check_docker_compose() {
    print_status "Checking Docker Compose..."
    
    if docker compose version &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker compose"
        print_success "Docker Compose (v2) is available"
    elif command -v docker-compose &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker-compose"
        print_success "Docker Compose (v1) is available"
    else
        print_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
}

# Create environment file
setup_environment() {
    print_status "Setting up environment configuration..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            print_success "Created .env file from .env.example"
            print_warning "Please review and update the .env file with your specific configuration"
        else
            print_error ".env.example file not found"
            exit 1
        fi
    else
        print_warning ".env file already exists, skipping creation"
    fi
}

# Create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    directories=(
        "uploads"
        "database/migrations"
        "database/seeds"
        "logs"
        "nginx"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_success "Created directory: $dir"
        fi
    done
}

# Build Docker images
build_images() {
    print_status "Building Docker images..."
    
    # Check if frontend and backend directories exist
    if [ ! -d "frontend" ] || [ ! -d "backend" ]; then
        print_warning "Frontend or backend directories not found. Skipping image build."
        print_warning "Please run the repository restructuring first."
        return
    fi
    
    $DOCKER_COMPOSE_CMD -f docker-compose.yml -f docker-compose.dev.yml build
    print_success "Docker images built successfully"
}

# Start services
start_services() {
    print_status "Starting development services..."
    
    # Start database and Redis first
    print_status "Starting database and Redis..."
    $DOCKER_COMPOSE_CMD -f docker-compose.yml -f docker-compose.dev.yml up -d postgres redis
    
    # Wait for database to be ready
    print_status "Waiting for database to be ready..."
    sleep 10
    
    # Start all services
    print_status "Starting all services..."
    $DOCKER_COMPOSE_CMD -f docker-compose.yml -f docker-compose.dev.yml up -d
    
    print_success "All services started successfully"
}

# Show service status
show_status() {
    print_status "Checking service status..."
    $DOCKER_COMPOSE_CMD -f docker-compose.yml -f docker-compose.dev.yml ps
}

# Show service logs
show_logs() {
    print_status "Recent logs from all services:"
    $DOCKER_COMPOSE_CMD -f docker-compose.yml -f docker-compose.dev.yml logs --tail=20
}

# Show connection information
show_connection_info() {
    print_header "🚀 TenxCFO Development Environment Ready!"
    
    echo "📱 Frontend Application:"
    echo "   URL: http://localhost:3000"
    echo ""
    echo "🔧 Backend API:"
    echo "   URL: http://localhost:4000"
    echo "   Health Check: http://localhost:4000/api/health"
    echo ""
    echo "🗄️  PostgreSQL Database:"
    echo "   Host: localhost"
    echo "   Port: 5432"
    echo "   Database: tenxcfo"
    echo "   Username: tenxcfo_user"
    echo "   Password: tenxcfo_password"
    echo ""
    echo "🔴 Redis Cache:"
    echo "   Host: localhost"
    echo "   Port: 6379"
    echo ""
    echo "📋 Useful Commands:"
    echo "   View logs: $DOCKER_COMPOSE_CMD -f docker-compose.yml -f docker-compose.dev.yml logs -f"
    echo "   Stop services: $DOCKER_COMPOSE_CMD -f docker-compose.yml -f docker-compose.dev.yml down"
    echo "   Restart services: $DOCKER_COMPOSE_CMD -f docker-compose.yml -f docker-compose.dev.yml restart"
    echo "   Shell into backend: $DOCKER_COMPOSE_CMD -f docker-compose.yml -f docker-compose.dev.yml exec backend sh"
    echo "   Shell into frontend: $DOCKER_COMPOSE_CMD -f docker-compose.yml -f docker-compose.dev.yml exec frontend sh"
    echo ""
    echo "🔍 Troubleshooting:"
    echo "   If services fail to start, check logs with: $DOCKER_COMPOSE_CMD logs [service-name]"
    echo "   To rebuild images: $DOCKER_COMPOSE_CMD build --no-cache"
    echo ""
}

# Main setup function
main() {
    print_header "🏗️  TenxCFO Development Environment Setup"
    
    check_docker
    check_docker_compose
    setup_environment
    create_directories
    
    # Ask user if they want to build and start services
    echo ""
    read -p "Do you want to build and start the services now? (y/N): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        build_images
        start_services
        sleep 5
        show_status
        show_connection_info
    else
        print_success "Environment setup completed!"
        print_status "To start services later, run:"
        echo "  $DOCKER_COMPOSE_CMD -f docker-compose.yml -f docker-compose.dev.yml up -d"
    fi
}

# Handle script arguments
case "${1:-setup}" in
    "setup")
        main
        ;;
    "start")
        check_docker
        check_docker_compose
        start_services
        show_connection_info
        ;;
    "stop")
        check_docker_compose
        print_status "Stopping all services..."
        $DOCKER_COMPOSE_CMD -f docker-compose.yml -f docker-compose.dev.yml down
        print_success "All services stopped"
        ;;
    "restart")
        check_docker_compose
        print_status "Restarting all services..."
        $DOCKER_COMPOSE_CMD -f docker-compose.yml -f docker-compose.dev.yml restart
        show_connection_info
        ;;
    "logs")
        check_docker_compose
        $DOCKER_COMPOSE_CMD -f docker-compose.yml -f docker-compose.dev.yml logs -f
        ;;
    "status")
        check_docker_compose
        show_status
        ;;
    "clean")
        check_docker_compose
        print_warning "This will remove all containers, volumes, and images. Are you sure? (y/N)"
        read -p "" -n 1 -r
        echo ""
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            $DOCKER_COMPOSE_CMD -f docker-compose.yml -f docker-compose.dev.yml down -v --rmi all
            print_success "Environment cleaned"
        fi
        ;;
    "help")
        echo "TenxCFO Development Setup Script"
        echo ""
        echo "Usage: $0 [command]"
        echo ""
        echo "Commands:"
        echo "  setup     - Initial setup (default)"
        echo "  start     - Start all services"
        echo "  stop      - Stop all services"
        echo "  restart   - Restart all services"
        echo "  logs      - Show and follow logs"
        echo "  status    - Show service status"
        echo "  clean     - Remove all containers and volumes"
        echo "  help      - Show this help message"
        ;;
    *)
        print_error "Unknown command: $1"
        echo "Run '$0 help' for available commands"
        exit 1
        ;;
esac

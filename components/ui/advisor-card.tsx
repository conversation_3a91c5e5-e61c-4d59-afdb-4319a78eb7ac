import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "./card";
import { <PERSON><PERSON> } from "./button";
import { Badge } from "./badge";
import { Star } from "lucide-react";
import React from "react";

interface Advisor {
  id: string;
  name: string;
  title: string;
  rating: number;
  reviews: number;
  specialties: string[];
  imageUrl: string;
}

interface AdvisorCardProps {
  advisor: Advisor;
  isSelected: boolean;
  onSelect: (id: string) => void;
  onViewProfile: (id: string) => void;
}

export function AdvisorCard({
  advisor,
  isSelected,
  onSelect,
  onViewProfile,
}: AdvisorCardProps) {
  return (
    <Card
      className={`cursor-pointer transition-all duration-200 ${isSelected
          ? "border-black ring-2 ring-black shadow-lg"
          : "border-gray-200 hover:border-gray-400"
        } bg-white text-black`}
      onClick={() => onSelect(advisor.id)}
    >
      <CardContent className="p-4">
        <div className="flex items-center space-x-4">
          <img
            src={advisor.imageUrl}
            alt={advisor.name}
            className="w-16 h-16 rounded-full object-cover border border-gray-200"
          />
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-black">{advisor.name}</h3>
            <p className="text-sm text-gray-600">{advisor.title}</p>
            <div className="flex items-center text-sm text-gray-700 mt-1">
              <Star className="w-4 h-4 fill-amber-400 text-amber-400 mr-1" />
              <span>{advisor.rating.toFixed(1)} ({advisor.reviews} reviews)</span>
            </div>
          </div>
        </div>
        <div className="mt-4 flex flex-wrap gap-2">
          {advisor.specialties.map((specialty) => (
            <Badge key={specialty} variant="outline">
              {specialty}
            </Badge>
          ))}
        </div>
        <Button
          variant="outline-black"
          className="mt-4 w-full"
          onClick={(e) => {
            e.stopPropagation();
            onViewProfile(advisor.id);
          }}
        >
          View Profile
        </Button>
      </CardContent>
    </Card>
  );
}

interface AdvisorListProps {
  advisors: Advisor[];
  selectedAdvisorId: string | null;
  onSelectAdvisor: (id: string) => void;
  onViewProfile: (id: string) => void;
}

export function AdvisorList({
  advisors,
  selectedAdvisorId,
  onSelectAdvisor,
  onViewProfile,
}: AdvisorListProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {advisors.map((advisor) => (
        <AdvisorCard
          key={advisor.id}
          advisor={advisor}
          isSelected={advisor.id === selectedAdvisorId}
          onSelect={onSelectAdvisor}
          onViewProfile={onViewProfile}
        />
      ))}
    </div>
  );
}
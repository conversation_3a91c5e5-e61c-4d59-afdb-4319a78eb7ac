"use client";

import Link from "next/link";

export default function Footer() {

  return (
    <footer className="bg-gray-100 border-t border-gray-200 py-16 px-4">
      <div className="container mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
          <div>
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-20 h-5 relative">
                <img
                  src="/tenxcfo.png"
                  alt="10xCFO Logo"
                  width={80}
                  height={20}
                  className="w-full h-full object-contain"
                />
              </div>
            </div>
            <p className="text-gray-600 mb-4">
              Accelerating SME growth through data-driven insights and expert mentorship.
            </p>
            <div className="flex space-x-4">
              <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 cursor-pointer">
                <span className="text-gray-600 text-sm">f</span>
              </div>
              <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 cursor-pointer">
                <span className="text-gray-600 text-sm">t</span>
              </div>
              <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 cursor-pointer">
                <span className="text-gray-600 text-sm">in</span>
              </div>
            </div>
          </div>
          <div>
            <h4 className="text-h6 text-black mb-4">Platform</h4>
            <ul className="space-y-2 text-gray-600">
              <li><Link href="/sme" className="hover:text-black transition-colors">For SMEs</Link></li>
              <li><Link href="/investor" className="hover:text-black transition-colors">For Investors</Link></li>
              <li><Link href="/consultant" className="hover:text-black transition-colors">For Consultants</Link></li>
              <li><Link href="/10x-growth-hack" className="hover:text-black transition-colors">10X Growth</Link></li>
            </ul>
          </div>
          <div>
            <h4 className="text-h6 text-black mb-4">Resources</h4>
            <ul className="space-y-2 text-gray-600">
              <li><a href="#" className="hover:text-black transition-colors">Documentation</a></li>
              <li><a href="#" className="hover:text-black transition-colors">Case Studies</a></li>
              <li><a href="#" className="hover:text-black transition-colors">Blog</a></li>
            </ul>
          </div>
          <div>
            <h4 className="text-h6 text-black mb-4">Company</h4>
            <ul className="space-y-2 text-gray-600">
              <li><a href="#" className="hover:text-black transition-colors">About</a></li>
              <li><a href="#" className="hover:text-black transition-colors">Contact</a></li>
              <li><a href="#" className="hover:text-black transition-colors">Privacy</a></li>
              <li><a href="#" className="hover:text-black transition-colors">Terms</a></li>
            </ul>
          </div>
        </div>
        <div className="border-t border-gray-200 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-body-small text-gray-600">&copy; 2024 10xCFO. All rights reserved.</p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <a href="#" className="text-body-small text-gray-600 hover:text-black transition-colors">Privacy Policy</a>
            <a href="#" className="text-body-small text-gray-600 hover:text-black transition-colors">Terms of Service</a>
            <a href="#" className="text-body-small text-gray-600 hover:text-black transition-colors">Cookie Policy</a>
          </div>
        </div>
      </div>
    </footer>
  );
}

-- Initial seed data for TenxCFO development

-- Insert test users (passwords are hashed for 'password123')
INSERT INTO users (id, email, password_hash, name, role, status, profile_complete, email_verified) VALUES
(
  'admin-user-id-1',
  '<EMAIL>',
  '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.6', -- password123
  'Admin User',
  'admin',
  'active',
  true,
  true
),
(
  'sme-user-id-1',
  '<EMAIL>',
  '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.6', -- password123
  '<PERSON>',
  'sme',
  'active',
  true,
  true
),
(
  'consultant-user-id-1',
  '<EMAIL>',
  '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.6', -- password123
  '<PERSON>',
  'consultant',
  'active',
  true,
  true
),
(
  'investor-user-id-1',
  '<EMAIL>',
  '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.6', -- password123
  'Jane Investor',
  'investor',
  'active',
  true,
  true
);

-- Insert test companies
INSERT INTO companies (id, name, registration_number, industry, location, website, phone, year_established, employee_count, annual_revenue, description, owner_id) VALUES
(
  'company-1',
  'TechCorp Solutions',
  'TC123456789',
  'Technology',
  'Bangalore, India',
  'https://techcorp.com',
  '+91 98765 43210',
  2020,
  '25-50',
  3200000.00,
  'AI-powered customer service automation platform',
  'sme-user-id-1'
),
(
  'company-2',
  'GreenEnergy Ltd',
  'GE987654321',
  'Renewable Energy',
  'Mumbai, India',
  'https://greenenergy.com',
  '+91 87654 32109',
  2019,
  '50-100',
  5200000.00,
  'Solar and wind energy solutions provider',
  'sme-user-id-1'
);

-- Insert sample documents
INSERT INTO documents (id, name, original_name, file_path, file_size, mime_type, category, status, uploaded_by, company_id, metadata) VALUES
(
  'doc-1',
  'Financial Statements 2023',
  'financial_statements_2023.pdf',
  '/uploads/company-1/financial_statements_2023.pdf',
  2048576,
  'application/pdf',
  'financial_statements',
  'completed',
  'sme-user-id-1',
  'company-1',
  '{"year": 2023, "pages": 15}'
),
(
  'doc-2',
  'Tax Returns 2023',
  'tax_returns_2023.pdf',
  '/uploads/company-1/tax_returns_2023.pdf',
  1536000,
  'application/pdf',
  'tax_returns',
  'completed',
  'sme-user-id-1',
  'company-1',
  '{"year": 2023, "pages": 8}'
),
(
  'doc-3',
  'Bank Statements Q4 2023',
  'bank_statements_q4_2023.pdf',
  '/uploads/company-1/bank_statements_q4_2023.pdf',
  1024000,
  'application/pdf',
  'bank_statements',
  'processing',
  'sme-user-id-1',
  'company-1',
  '{"quarter": "Q4", "year": 2023}'
);

-- Insert sample reports
INSERT INTO reports (id, title, type, status, company_id, requested_by, generated_by, report_data, external_url) VALUES
(
  'report-1',
  'Financial Health Analysis - TechCorp',
  'financial_health',
  'completed',
  'company-1',
  'sme-user-id-1',
  'admin-user-id-1',
  '{"score": 85, "breakdown": {"financial": 90, "operational": 80, "market": 85, "growth": 85}, "recommendations": ["Improve cash flow management", "Diversify revenue streams"]}',
  'https://reports.tenxcfo.com/report/1'
),
(
  'report-2',
  'Growth Potential Assessment - TechCorp',
  'growth_analysis',
  'processing',
  'company-1',
  'sme-user-id-1',
  null,
  null,
  null
),
(
  'report-3',
  'Investment Readiness Report - GreenEnergy',
  'investment_readiness',
  'pending',
  'company-2',
  'sme-user-id-1',
  null,
  null,
  null
);

-- Insert user-company relationships
INSERT INTO user_companies (user_id, company_id, relationship_type) VALUES
('consultant-user-id-1', 'company-1', 'consultant'),
('investor-user-id-1', 'company-1', 'investor');

-- Insert sample audit logs
INSERT INTO audit_logs (user_id, action, resource_type, resource_id, details, ip_address) VALUES
('sme-user-id-1', 'CREATE', 'document', 'doc-1', '{"filename": "financial_statements_2023.pdf"}', '*************'),
('sme-user-id-1', 'CREATE', 'document', 'doc-2', '{"filename": "tax_returns_2023.pdf"}', '*************'),
('admin-user-id-1', 'CREATE', 'report', 'report-1', '{"type": "financial_health"}', '************'),
('admin-user-id-1', 'UPDATE', 'report', 'report-1', '{"status": "completed"}', '************');

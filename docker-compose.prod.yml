version: '3.8'

# Production overrides for docker-compose.yml
# Usage: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up

services:
  # Production backend configuration
  backend:
    build:
      target: production
    environment:
      NODE_ENV: production
      LOG_LEVEL: info
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Production frontend configuration
  frontend:
    build:
      target: production
    environment:
      NODE_ENV: production
    restart: always
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Production database configuration
  postgres:
    restart: always
    environment:
      POSTGRES_DB: tenxcfo_prod
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Production Redis configuration
  redis:
    restart: always
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Nginx reverse proxy for production
  nginx:
    image: nginx:alpine
    container_name: tenxcfo-nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    networks:
      - tenxcfo-network
    depends_on:
      - frontend
      - backend
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

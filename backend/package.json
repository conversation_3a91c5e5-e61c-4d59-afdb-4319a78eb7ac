{"name": "tenxcfo-backend", "version": "1.0.0", "description": "Backend API for TenxCFO platform", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "typecheck": "tsc --noEmit", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js", "db:reset": "npm run db:migrate && npm run db:seed"}, "keywords": ["tenxcfo", "api", "backend", "express", "typescript", "postgresql"], "author": "TenxCFO Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "multer": "^1.4.5-lts.1", "pg": "^8.11.3", "redis": "^4.6.10", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "uuid": "^9.0.1", "mime-types": "^2.1.35", "sharp": "^0.32.6", "node-cron": "^3.0.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.8.10", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/pg": "^8.10.7", "@types/uuid": "^9.0.7", "@types/mime-types": "^2.1.4", "@types/node-cron": "^3.0.11", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.2.2", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "eslint": "^8.53.0", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "prettier": "^3.0.3", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}
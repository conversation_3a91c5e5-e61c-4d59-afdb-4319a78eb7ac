import { logger } from '@/utils/logger';
import { Request, Response, Router } from 'express';

const router = Router();

// Health check endpoint
router.get('/', (req: Request, res: Response) => {
  const healthCheck = {
    uptime: process.uptime(),
    message: 'OK',
    timestamp: new Date().toISOString(),
    environment: process.env['NODE_ENV'],
    version: '1.0.0',
    memory: {
      used: Math.round((process.memoryUsage().heapUsed / 1024 / 1024) * 100) / 100,
      total: Math.round((process.memoryUsage().heapTotal / 1024 / 1024) * 100) / 100,
    },
  };

  try {
    res.status(200).json(healthCheck);
  } catch (error) {
    logger.error('Health check failed:', error);
    res.status(503).json({
      message: 'Service Unavailable',
      timestamp: new Date().toISOString(),
    });
  }
});

// Detailed health check with dependencies
router.get('/detailed', async (req: Request, res: Response) => {
  const checks = {
    server: 'OK',
    database: 'Unknown',
    redis: 'Unknown',
    timestamp: new Date().toISOString(),
  };

  let status = 200;

  try {
    // TODO: Add database connection check
    // checks.database = await checkDatabaseConnection() ? 'OK' : 'Error';
    
    // TODO: Add Redis connection check
    // checks.redis = await checkRedisConnection() ? 'OK' : 'Error';

    // If any service is down, return 503
    if (Object.values(checks).includes('Error')) {
      status = 503;
    }

    res.status(status).json({
      status: status === 200 ? 'healthy' : 'unhealthy',
      checks,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    });
  } catch (error) {
    logger.error('Detailed health check failed:', error);
    res.status(503).json({
      status: 'unhealthy',
      error: 'Health check failed',
      timestamp: new Date().toISOString(),
    });
  }
});

export { router as healthRoutes };

// Common types for the TenxCFO backend

export type UserRole = 'admin' | 'sme' | 'consultant' | 'investor';
export type UserStatus = 'active' | 'inactive' | 'pending' | 'suspended';
export type DocumentStatus = 'processing' | 'completed' | 'failed';
export type DocumentCategory = 'financial_statements' | 'tax_returns' | 'bank_statements' | 'business_registration' | 'gst_returns' | 'audit_reports' | 'other';
export type ReportStatus = 'pending' | 'processing' | 'completed' | 'failed';
export type ReportType = 'financial_health' | 'growth_analysis' | 'market_analysis' | 'investment_readiness';

export interface User {
  id: string;
  email: string;
  passwordHash: string;
  name: string;
  role: UserRole;
  status: UserStatus;
  profileComplete: boolean;
  emailVerified: boolean;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface Company {
  id: string;
  name: string;
  registrationNumber?: string;
  industry?: string;
  location?: string;
  website?: string;
  phone?: string;
  address?: string;
  yearEstablished?: number;
  employeeCount?: string;
  annualRevenue?: number;
  description?: string;
  ownerId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Document {
  id: string;
  name: string;
  originalName: string;
  filePath: string;
  fileSize: number;
  mimeType: string;
  category: DocumentCategory;
  status: DocumentStatus;
  uploadedBy: string;
  companyId: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface Report {
  id: string;
  title: string;
  type: ReportType;
  status: ReportStatus;
  companyId: string;
  requestedBy: string;
  generatedBy?: string;
  reportData?: Record<string, any>;
  filePath?: string;
  externalUrl?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Session {
  id: string;
  userId: string;
  tokenHash: string;
  expiresAt: Date;
  createdAt: Date;
  lastUsed: Date;
}

export interface AuditLog {
  id: string;
  userId?: string;
  action: string;
  resourceType: string;
  resourceId?: string;
  details?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
}

// Request/Response types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  name: string;
  role: UserRole;
  companyName?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: {
    message: string;
    statusCode: number;
    stack?: string;
  };
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginationResponse {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

// Express Request extensions
declare global {
  namespace Express {
    interface Request {
      user?: User;
      pagination?: PaginationParams;
    }
  }
}

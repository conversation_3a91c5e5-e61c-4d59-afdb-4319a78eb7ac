import Joi from 'joi';
import { UserRole } from '@/types';

// Common validation schemas
export const emailSchema = Joi.string().email().required().messages({
  'string.email': 'Please provide a valid email address',
  'any.required': 'Email is required',
});

export const passwordSchema = Joi.string()
  .min(8)
  .pattern(new RegExp('^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)'))
  .required()
  .messages({
    'string.min': 'Password must be at least 8 characters long',
    'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
    'any.required': 'Password is required',
  });

export const userRoleSchema = Joi.string()
  .valid('admin', 'sme', 'consultant', 'investor')
  .required()
  .messages({
    'any.only': 'Role must be one of: admin, sme, consultant, investor',
    'any.required': 'Role is required',
  });

export const uuidSchema = Joi.string()
  .uuid()
  .required()
  .messages({
    'string.uuid': 'Invalid ID format',
    'any.required': 'ID is required',
  });

// Authentication schemas
export const loginSchema = Joi.object({
  email: emailSchema,
  password: Joi.string().required().messages({
    'any.required': 'Password is required',
  }),
});

export const registerSchema = Joi.object({
  email: emailSchema,
  password: passwordSchema,
  name: Joi.string().min(2).max(100).required().messages({
    'string.min': 'Name must be at least 2 characters long',
    'string.max': 'Name cannot exceed 100 characters',
    'any.required': 'Name is required',
  }),
  role: userRoleSchema,
  companyName: Joi.string().min(2).max(200).optional().messages({
    'string.min': 'Company name must be at least 2 characters long',
    'string.max': 'Company name cannot exceed 200 characters',
  }),
});

// User schemas
export const updateUserSchema = Joi.object({
  name: Joi.string().min(2).max(100).optional(),
  email: Joi.string().email().optional(),
  role: Joi.string().valid('admin', 'sme', 'consultant', 'investor').optional(),
  status: Joi.string().valid('active', 'inactive', 'pending', 'suspended').optional(),
  profileComplete: Joi.boolean().optional(),
}).min(1).messages({
  'object.min': 'At least one field must be provided for update',
});

// Document schemas
export const documentCategorySchema = Joi.string()
  .valid('financial_statements', 'tax_returns', 'bank_statements', 'business_registration', 'gst_returns', 'audit_reports', 'other')
  .required()
  .messages({
    'any.only': 'Invalid document category',
    'any.required': 'Document category is required',
  });

export const updateDocumentStatusSchema = Joi.object({
  status: Joi.string()
    .valid('processing', 'completed', 'failed')
    .required()
    .messages({
      'any.only': 'Status must be one of: processing, completed, failed',
      'any.required': 'Status is required',
    }),
});

// Report schemas
export const generateReportSchema = Joi.object({
  title: Joi.string().min(3).max(200).required().messages({
    'string.min': 'Title must be at least 3 characters long',
    'string.max': 'Title cannot exceed 200 characters',
    'any.required': 'Title is required',
  }),
  type: Joi.string()
    .valid('financial_health', 'growth_analysis', 'market_analysis', 'investment_readiness')
    .required()
    .messages({
      'any.only': 'Invalid report type',
      'any.required': 'Report type is required',
    }),
  companyId: uuidSchema,
});

export const updateReportSchema = Joi.object({
  title: Joi.string().min(3).max(200).optional(),
  status: Joi.string().valid('pending', 'processing', 'completed', 'failed').optional(),
  reportData: Joi.object().optional(),
  externalUrl: Joi.string().uri().optional(),
}).min(1).messages({
  'object.min': 'At least one field must be provided for update',
});

// Pagination schema
export const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  sortBy: Joi.string().optional(),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
});

// Validation middleware helper
export const validateSchema = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const validationErrors = error.details.map((detail) => ({
        field: detail.path.join('.'),
        message: detail.message,
      }));

      return res.status(422).json({
        success: false,
        error: {
          message: 'Validation failed',
          statusCode: 422,
        },
        data: {
          validationErrors,
        },
      });
    }

    req.body = value;
    next();
  };
};

export const validateQuery = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.query, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const validationErrors = error.details.map((detail) => ({
        field: detail.path.join('.'),
        message: detail.message,
      }));

      return res.status(422).json({
        success: false,
        error: {
          message: 'Query validation failed',
          statusCode: 422,
        },
        data: {
          validationErrors,
        },
      });
    }

    req.query = value;
    next();
  };
};

export const validateParams = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.params, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const validationErrors = error.details.map((detail) => ({
        field: detail.path.join('.'),
        message: detail.message,
      }));

      return res.status(422).json({
        success: false,
        error: {
          message: 'Parameter validation failed',
          statusCode: 422,
        },
        data: {
          validationErrors,
        },
      });
    }

    req.params = value;
    next();
  };
};

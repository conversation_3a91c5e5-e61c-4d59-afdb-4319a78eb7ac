import { Response } from 'express';
import { ApiResponse, PaginationResponse } from '@/types';

/**
 * Utility functions for standardized API responses
 */

export const sendSuccess = <T>(
  res: Response,
  data?: T,
  message?: string,
  statusCode: number = 200
): void => {
  const response: ApiResponse<T> = {
    success: true,
    message,
    data,
  };

  res.status(statusCode).json(response);
};

export const sendError = (
  res: Response,
  message: string,
  statusCode: number = 500,
  error?: any
): void => {
  const response: ApiResponse = {
    success: false,
    error: {
      message,
      statusCode,
    },
  };

  // Include stack trace in development
  if (process.env.NODE_ENV === 'development' && error?.stack) {
    response.error!.stack = error.stack;
  }

  res.status(statusCode).json(response);
};

export const sendPaginatedResponse = <T>(
  res: Response,
  data: T[],
  pagination: PaginationResponse,
  message?: string
): void => {
  const response: ApiResponse<{ items: T[]; pagination: PaginationResponse }> = {
    success: true,
    message,
    data: {
      items: data,
      pagination,
    },
  };

  res.status(200).json(response);
};

export const sendCreated = <T>(
  res: Response,
  data?: T,
  message: string = 'Resource created successfully'
): void => {
  sendSuccess(res, data, message, 201);
};

export const sendNoContent = (res: Response): void => {
  res.status(204).send();
};

export const sendNotFound = (
  res: Response,
  message: string = 'Resource not found'
): void => {
  sendError(res, message, 404);
};

export const sendBadRequest = (
  res: Response,
  message: string = 'Bad request'
): void => {
  sendError(res, message, 400);
};

export const sendUnauthorized = (
  res: Response,
  message: string = 'Unauthorized'
): void => {
  sendError(res, message, 401);
};

export const sendForbidden = (
  res: Response,
  message: string = 'Forbidden'
): void => {
  sendError(res, message, 403);
};

export const sendConflict = (
  res: Response,
  message: string = 'Resource already exists'
): void => {
  sendError(res, message, 409);
};

export const sendValidationError = (
  res: Response,
  errors: any,
  message: string = 'Validation failed'
): void => {
  const response: ApiResponse = {
    success: false,
    error: {
      message,
      statusCode: 422,
    },
    data: {
      validationErrors: errors,
    },
  };

  res.status(422).json(response);
};

#!/usr/bin/env node

/**
 * Database Seeding Script
 * Seeds the database with initial development data
 */

const fs = require('fs');
const path = require('path');
const { Client } = require('pg');

// Database configuration
const dbConfig = {
  host: process.env.POSTGRES_HOST || 'localhost',
  port: process.env.POSTGRES_PORT || 5432,
  database: process.env.POSTGRES_DB || 'tenxcfo',
  user: process.env.POSTGRES_USER || 'tenxcfo_user',
  password: process.env.POSTGRES_PASSWORD || 'tenxcfo_password',
};

async function seedDatabase() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('Connected to database');

    // Check if data already exists
    const userCount = await client.query('SELECT COUNT(*) FROM users');
    const existingUsers = parseInt(userCount.rows[0].count);

    if (existingUsers > 0) {
      console.log(`Database already has ${existingUsers} users. Skipping seed.`);
      console.log('To force re-seed, truncate tables first or reset the database.');
      return;
    }

    // Read seed file
    const seedPath = path.join(__dirname, '../../database/seeds/initial_data.sql');
    
    if (!fs.existsSync(seedPath)) {
      console.log('No seed file found at:', seedPath);
      return;
    }

    const seedSQL = fs.readFileSync(seedPath, 'utf8');

    console.log('Seeding database with initial data...');

    try {
      // Begin transaction
      await client.query('BEGIN');
      
      // Execute seed data
      await client.query(seedSQL);
      
      // Commit transaction
      await client.query('COMMIT');
      
      console.log('✅ Database seeded successfully');
      
      // Show summary
      const summary = await client.query(`
        SELECT 
          (SELECT COUNT(*) FROM users) as users,
          (SELECT COUNT(*) FROM companies) as companies,
          (SELECT COUNT(*) FROM documents) as documents,
          (SELECT COUNT(*) FROM reports) as reports
      `);
      
      const counts = summary.rows[0];
      console.log('\n📊 Seed Summary:');
      console.log(`   Users: ${counts.users}`);
      console.log(`   Companies: ${counts.companies}`);
      console.log(`   Documents: ${counts.documents}`);
      console.log(`   Reports: ${counts.reports}`);
      
    } catch (error) {
      // Rollback transaction
      await client.query('ROLLBACK');
      console.error('❌ Seeding failed');
      console.error(error.message);
      throw error;
    }

  } catch (error) {
    console.error('Seed error:', error.message);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run seeding if called directly
if (require.main === module) {
  seedDatabase().catch(console.error);
}

module.exports = { seedDatabase };

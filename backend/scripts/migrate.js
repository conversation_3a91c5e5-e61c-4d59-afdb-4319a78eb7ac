#!/usr/bin/env node

/**
 * Database Migration Script
 * Runs pending migrations in the database/migrations directory
 */

const fs = require('fs');
const path = require('path');
const { Client } = require('pg');

// Database configuration
const dbConfig = {
  host: process.env.POSTGRES_HOST || 'localhost',
  port: process.env.POSTGRES_PORT || 5432,
  database: process.env.POSTGRES_DB || 'tenxcfo',
  user: process.env.POSTGRES_USER || 'tenxcfo_user',
  password: process.env.POSTGRES_PASSWORD || 'tenxcfo_password',
};

async function runMigrations() {
  const client = new Client(dbConfig);
  
  try {
    await client.connect();
    console.log('Connected to database');

    // Ensure migrations table exists
    await client.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL UNIQUE,
        executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Get executed migrations
    const executedResult = await client.query('SELECT name FROM migrations ORDER BY id');
    const executedMigrations = executedResult.rows.map(row => row.name);

    // Get migration files
    const migrationsDir = path.join(__dirname, '../../database/migrations');
    
    if (!fs.existsSync(migrationsDir)) {
      console.log('No migrations directory found, creating it...');
      fs.mkdirSync(migrationsDir, { recursive: true });
      console.log('Migrations directory created');
      return;
    }

    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort();

    if (migrationFiles.length === 0) {
      console.log('No migration files found');
      return;
    }

    // Run pending migrations
    let migrationsRun = 0;
    
    for (const file of migrationFiles) {
      const migrationName = path.basename(file, '.sql');
      
      if (executedMigrations.includes(migrationName)) {
        console.log(`Skipping already executed migration: ${migrationName}`);
        continue;
      }

      console.log(`Running migration: ${migrationName}`);
      
      const migrationPath = path.join(migrationsDir, file);
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

      try {
        // Begin transaction
        await client.query('BEGIN');
        
        // Execute migration
        await client.query(migrationSQL);
        
        // Record migration
        await client.query(
          'INSERT INTO migrations (name) VALUES ($1)',
          [migrationName]
        );
        
        // Commit transaction
        await client.query('COMMIT');
        
        console.log(`✅ Migration completed: ${migrationName}`);
        migrationsRun++;
        
      } catch (error) {
        // Rollback transaction
        await client.query('ROLLBACK');
        console.error(`❌ Migration failed: ${migrationName}`);
        console.error(error.message);
        throw error;
      }
    }

    if (migrationsRun === 0) {
      console.log('✅ All migrations are up to date');
    } else {
      console.log(`✅ Successfully ran ${migrationsRun} migration(s)`);
    }

  } catch (error) {
    console.error('Migration error:', error.message);
    process.exit(1);
  } finally {
    await client.end();
  }
}

// Run migrations if called directly
if (require.main === module) {
  runMigrations().catch(console.error);
}

module.exports = { runMigrations };

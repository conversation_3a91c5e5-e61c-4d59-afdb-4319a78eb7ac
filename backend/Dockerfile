# Backend Dockerfile for TenxCFO Express.js API

# Use Node.js 18 Alpine as base image
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json ./
COPY package-lock.json* ./
RUN \
  if [ -f package-lock.json ]; then npm ci --only=production; \
  else npm install --only=production; \
  fi

# Development image
FROM base AS development
WORKDIR /app

# Install all dependencies (including devDependencies)
COPY package.json ./
COPY package-lock.json* ./
RUN if [ -f package-lock.json ]; then npm ci; else npm install; fi

# Install additional tools for development
RUN npm install -g nodemon ts-node

# Copy source code
COPY . .

# Create uploads directory
RUN mkdir -p uploads

# Create a non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 apiuser

# Change ownership of the app directory
RUN chown -R apiuser:nodejs /app

USER apiuser

# Expose port
EXPOSE 4000

# Set environment variables
ENV NODE_ENV=development
ENV PORT=4000

# Start the development server
CMD ["npm", "run", "dev"]

# Build stage for production
FROM base AS builder
WORKDIR /app

# Copy package files
COPY package.json ./
COPY package-lock.json* ./

# Install all dependencies
RUN if [ -f package-lock.json ]; then npm ci; else npm install; fi

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production image
FROM base AS production
WORKDIR /app

ENV NODE_ENV=production
ENV PORT=4000

# Create a non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 apiuser

# Copy production dependencies
COPY --from=deps /app/node_modules ./node_modules

# Copy built application
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/package.json ./package.json

# Create uploads directory
RUN mkdir -p uploads

# Change ownership
RUN chown -R apiuser:nodejs /app

USER apiuser

# Expose port
EXPOSE 4000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:4000/api/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Start the production server
CMD ["npm", "start"]

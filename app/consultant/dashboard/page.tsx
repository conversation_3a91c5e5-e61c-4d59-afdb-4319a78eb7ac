"use client";

import AppHeader from "@/components/navigation/AppHeader";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuthStore } from "@/stores/authStore";
import { motion } from "framer-motion";
import {
    ArrowRight,
    Calendar,
    Mail,
    Phone,
    Users
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function ConsultantDashboard() {
  const router = useRouter();
  const { logout, user } = useAuthStore();

  const handleLogout = () => {
    logout();
    router.push('/');
  };
  // Mock data
  const stats = {
    totalClients: 24,
    activeClients: 18
  };

  const clients = [
    {
      id: 1,
      name: "TechStart Solutions",
      industry: "Technology",
      currentScore: 68,
      initialScore: 45,
      improvement: 23,
      status: "active",
      lastContact: "2 days ago",
      nextMilestone: "Upload Q4 financials"
    },
    {
      id: 2,
      name: "GreenManufacturing Ltd",
      industry: "Manufacturing",
      currentScore: 78,
      initialScore: 62,
      improvement: 16,
      status: "completed",
      lastContact: "1 week ago",
      nextMilestone: "Investor presentation ready"
    },
    {
      id: 3,
      name: "HealthCare Innovations",
      industry: "Healthcare",
      currentScore: 55,
      initialScore: 42,
      improvement: 13,
      status: "needs_attention",
      lastContact: "5 days ago",
      nextMilestone: "Complete compliance documents"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-100 text-green-700 border-green-200";
      case "completed": return "bg-blue-100 text-blue-700 border-blue-200";
      case "needs_attention": return "bg-amber-100 text-amber-700 border-amber-200";
      default: return "bg-gray-100 text-gray-600 border-gray-200";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="consultant" />

      <div className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-h1 text-black mb-2">Welcome back, {user?.name || 'Consultant'}</h1>
              <p className="text-body text-gray-600">Manage your clients and track your consulting performance</p>
            </div>
            <Badge className="bg-amber-100 text-amber-700 border-amber-200">
              Certified Consultant
            </Badge>
          </div>
        </motion.div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Users className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
                <div className="text-3xl font-bold text-black mb-1">{stats.totalClients}</div>
                <p className="text-gray-600">Total Clients</p>
                <p className="text-sm text-gray-500 mt-1">{stats.activeClients} active</p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <Users className="w-6 h-6 text-green-600" />
                  </div>
                </div>
                <div className="text-3xl font-bold text-black mb-1">{stats.activeClients}</div>
                <p className="text-gray-600">Active Clients</p>
                <p className="text-sm text-gray-500 mt-1">Currently engaged</p>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Client Management */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="lg:col-span-2"
          >
            <Card className="bg-white border-gray-200 shadow-lg">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-black flex items-center">
                      <Users className="w-5 h-5 mr-2 text-black" />
                      Active Clients
                    </CardTitle>
                    <CardDescription className="text-gray-600">
                      Monitor your clients&apos; progress and next steps
                    </CardDescription>
                  </div>
                  <Button size="sm" className="bg-black hover:bg-gray-800 text-white">
                    Add Client
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {clients.map((client) => (
                    <div
                      key={client.id}
                      className="p-6 bg-gray-50 rounded-lg border border-gray-200"
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-h5 text-black">{client.name}</h3>
                            <Badge className={getStatusColor(client.status)}>
                              {client.status.replace('_', ' ')}
                            </Badge>
                          </div>
                          <p className="text-gray-600 text-sm mb-2">{client.industry}</p>
                          <p className="text-gray-700 text-sm">{client.nextMilestone}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-black mb-1">{client.currentScore}</div>
                          <p className="text-gray-600 text-xs">Current Score</p>
                          <p className="text-black text-xs">+{client.improvement} improvement</p>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 text-gray-600 text-sm">
                          <span className="flex items-center">
                            <Calendar className="w-4 h-4 mr-1" />
                            Last contact: {client.lastContact}
                          </span>
                        </div>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm" className="border-gray-300 text-black hover:bg-gray-50">
                            <Phone className="w-4 h-4 mr-2" />
                            Call
                          </Button>
                          <Button variant="outline" size="sm" className="border-gray-300 text-black hover:bg-gray-50">
                            <Mail className="w-4 h-4 mr-2" />
                            Email
                          </Button>
                          <Link href={`/consultant/dashboard/client/${client.id}`}>
                            <Button size="sm" className="bg-black hover:bg-gray-800 text-white">
                              View Details
                              <ArrowRight className="w-4 h-4 ml-2" />
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-black text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Link href="/consultant/dashboard/onboarding">
                    <Button className="w-full !bg-black !text-white justify-start">
                      <Users className="w-4 h-4 mr-2" />
                      Onboard New Client
                    </Button>
                  </Link>
                  <Link href="/consultant/dashboard/clients">
                    <Button variant="outline" className="w-full border-gray-300 text-black hover:bg-gray-50 justify-start">
                      <Users className="w-4 h-4 mr-2" />
                      View All Clients
                    </Button>
                  </Link>
                  <Link href="/consultant/dashboard/schedule">
                    <Button variant="outline" className="w-full border-gray-300 text-black hover:bg-gray-50 justify-start">
                      <Calendar className="w-4 h-4 mr-2" />
                      Schedule Calls
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}

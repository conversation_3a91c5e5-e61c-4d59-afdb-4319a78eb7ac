"use client";

import AppHeader from "@/components/navigation/AppHeader";
import BackButton from "@/components/navigation/BackButton";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from "framer-motion";
import { Calendar, Clock, MapPin, Plus, Users, Video } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface Meeting {
  id: number;
  title: string;
  client: string;
  date: string;
  time: string;
  duration: string;
  type: "video" | "in-person" | "phone";
  location?: string;
  status: "upcoming" | "completed" | "cancelled";
  description: string;
}

export default function ConsultantSchedule() {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);

  // Mock data - replace with actual API call
  const meetings: Meeting[] = [
    {
      id: 1,
      title: "Financial Review Meeting",
      client: "TechCorp Solutions",
      date: "2024-02-15",
      time: "10:00",
      duration: "1 hour",
      type: "video",
      status: "upcoming",
      description: "Quarterly financial review and planning session"
    },
    {
      id: 2,
      title: "Strategy Planning Session",
      client: "GreenEnergy Ltd",
      date: "2024-02-15",
      time: "14:30",
      duration: "2 hours",
      type: "in-person",
      location: "Client Office, Mumbai",
      status: "upcoming",
      description: "Discuss growth strategy and market expansion plans"
    },
    {
      id: 3,
      title: "Project Kickoff",
      client: "HealthTech Innovations",
      date: "2024-02-16",
      time: "11:00",
      duration: "1.5 hours",
      type: "video",
      status: "upcoming",
      description: "Initial project discussion and requirement gathering"
    },
    {
      id: 4,
      title: "Monthly Check-in",
      client: "RetailCorp",
      date: "2024-02-14",
      time: "15:00",
      duration: "30 minutes",
      type: "phone",
      status: "completed",
      description: "Regular progress update and Q&A session"
    }
  ];

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "video": return <Video className="w-4 h-4" />;
      case "in-person": return <MapPin className="w-4 h-4" />;
      case "phone": return <Clock className="w-4 h-4" />;
      default: return <Calendar className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "upcoming": return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "completed": return "bg-green-500/20 text-green-400 border-green-500/30";
      case "cancelled": return "bg-red-500/20 text-red-400 border-red-500/30";
      default: return "bg-slate-500/20 text-slate-400 border-slate-500/30";
    }
  };

  const filteredMeetings = meetings.filter(meeting => meeting.date === selectedDate);
  const upcomingMeetings = meetings.filter(meeting => meeting.status === "upcoming");

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <AppHeader variant="consultant" />
      
      {/* Back Navigation */}
      <BackButton href="/consultant/dashboard" label="Back to Dashboard" />

      <div className="container mx-auto px-6 py-8">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-black mb-2">Schedule</h1>
            <p className="text-gray-600">Manage your meetings and appointments</p>
          </div>
          <Button className="bg-black hover:bg-gray-800">
            <Plus className="w-4 h-4 mr-2" />
            Schedule Meeting
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Calendar Section */}
          <div className="lg:col-span-2">
            <Card className="bg-white border-gray-200 shadow-sm mb-6">
              <CardHeader>
                <CardTitle className="text-black flex items-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Calendar
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4 mb-4">
                  <input
                    type="date"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                    className="bg-white border-gray-300 text-black rounded-md px-3 py-2"
                  />
                  <Badge variant="outline">
                    {filteredMeetings.length} meetings
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Today's Meetings */}
            <Card className="bg-white border-gray-200 shadow-sm">
  <CardHeader>
    <CardTitle className="text-black">
      Meetings for {new Date(selectedDate).toLocaleDateString()}
    </CardTitle>
  </CardHeader>
  <CardContent>
    {filteredMeetings.length > 0 ? (
      <div className="space-y-4">
        {filteredMeetings.map((meeting, index) => (
          <motion.div
            key={meeting.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="bg-gray-50 rounded-lg p-4 border border-gray-200"
          >
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                  {getTypeIcon(meeting.type)}
                </div>
                <div>
                  <h3 className="text-black font-semibold">{meeting.title}</h3>
                  <p className="text-gray-600 text-sm">{meeting.client}</p>
                </div>
              </div>
              <Badge className={getStatusColor(meeting.status)}>
                {meeting.status}
              </Badge>
            </div>

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4 text-gray-600" />
                <span className="text-gray-600">{meeting.time} ({meeting.duration})</span>
              </div>
              {meeting.location && (
                <div className="flex items-center space-x-2">
                  <MapPin className="w-4 h-4 text-gray-600" />
                  <span className="text-gray-600">{meeting.location}</span>
                </div>
              )}
            </div>

            <p className="text-gray-600 text-sm mt-3">{meeting.description}</p>

            <div className="flex gap-2 mt-4">
              <Button size="sm" variant="outline-gray">
                Edit
              </Button>
              {meeting.type === "video" && (
                <Button size="sm" className="bg-black hover:bg-gray-800">
                  Join Meeting
                </Button>
              )}
            </div>
          </motion.div>
        ))}
      </div>
    ) : (
      <div className="text-center py-8">
        <Calendar className="w-16 h-16 text-gray-600 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-black mb-2">No meetings scheduled</h3>
        <p className="text-gray-600">You have no meetings for this date</p>
      </div>
    )}
  </CardContent>
</Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Upcoming Meetings */}
            <Card className="bg-white border-gray-200 shadow-sm">
  <CardHeader>
    <CardTitle className="text-black text-lg">Upcoming Meetings</CardTitle>
  </CardHeader>
  <CardContent>
    <div className="space-y-3">
      {upcomingMeetings.slice(0, 5).map((meeting) => (
        <div key={meeting.id} className="bg-gray-50 rounded-lg p-3">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-black text-sm font-medium">{meeting.title}</h4>
            {getTypeIcon(meeting.type)}
          </div>
          <p className="text-gray-600 text-xs">{meeting.client}</p>
          <p className="text-gray-600 text-xs">
            {new Date(meeting.date).toLocaleDateString()} at {meeting.time}
          </p>
        </div>
      ))}
    </div>
  </CardContent>
</Card>

            {/* Quick Stats */}
            <Card className="bg-white border-gray-200 shadow-sm">
  <CardHeader>
    <CardTitle className="text-black text-lg">This Week</CardTitle>
  </CardHeader>
  <CardContent>
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Users className="w-4 h-4 text-blue-600" />
          <span className="text-gray-600 text-sm">Total Meetings</span>
        </div>
        <span className="text-black font-semibold">12</span>
      </div>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Clock className="w-4 h-4 text-green-600" />
          <span className="text-gray-600 text-sm">Hours Scheduled</span>
        </div>
        <span className="text-black font-semibold">18.5</span>
      </div>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Video className="w-4 h-4 text-purple-600" />
          <span className="text-gray-600 text-sm">Video Calls</span>
        </div>
        <span className="text-black font-semibold">8</span>
      </div>
    </div>
  </CardContent>
</Card>
          </div>
        </div>
      </div>
    </div>
  );
}

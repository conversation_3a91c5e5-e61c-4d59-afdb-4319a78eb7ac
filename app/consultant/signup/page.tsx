"use client";

import AppHeader from "@/components/navigation/AppHeader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Logo from "@/components/ui/logo";
import { MultiSelect } from "@/components/ui/multi-select";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";
import { motion } from "framer-motion";
import { ArrowLeft, ArrowRight, CheckCircle } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

// Import shared constants
const countries = [
  { code: "IN", name: "India", phoneCode: "+91" },
  { code: "AE", name: "UAE", phoneCode: "+971" },
  { code: "US", name: "United States", phoneCode: "+1" },
  { code: "GB", name: "United Kingdom", phoneCode: "+44" },
  { code: "CA", name: "Canada", phoneCode: "+1" },
  { code: "AU", name: "Australia", phoneCode: "+61" },
  { code: "SG", name: "Singapore", phoneCode: "+65" },
];

const consultantTypeOptions = [
  { value: "client-acquisition", label: "Client Acquisition / Lead Generating Consultant" },
  { value: "sector-specialist", label: "Sector Specialist" },
  { value: "fte-freelance", label: "FTE / Freelance 10X Growth Hack Consultant" },
  { value: "investment-facilitator", label: "Investment Facilitator" },
];

const consultingAreaOptions = [
  { value: "client-acquisition", label: "Client Acquisition & Lead Generation" },
  { value: "business-development", label: "Business Development Consultant" },
  { value: "ca-tax-advisory", label: "CA/CPA/Tax Advisory (SME Origination)" },
  { value: "referral-partner", label: "Referral & Partner Development" },
  { value: "sector-specialist", label: "Sector Specialist Consulting" },
  { value: "industry-advisory", label: "Industry-Specific SME Advisory (e.g., Agri, Manufacturing, IT, Healthcare, VFX, etc.)" },
  { value: "market-research", label: "Market Research & Competitive Intelligence" },
  { value: "sector-risk", label: "Sector Risk & Opportunity Mapping" },
  { value: "growth-acceleration", label: "Growth Acceleration Consulting" },
  { value: "growth-hack", label: "10X Growth Hack Consultant (FTE/Freelance)" },
  { value: "corporate-finance", label: "Corporate Finance Advisory" },
  { value: "strategy-operations", label: "Strategy & Operations Consulting" },
  { value: "vc-pe-advisory", label: "Venture Capital / Private Equity Advisory" },
  { value: "investment-banking", label: "Investment Banking / Deal Structuring Advisory" },
  { value: "capital-facilitation", label: "Capital Facilitation & Investment Advisory" },
  { value: "investment-facilitator", label: "Investment Facilitator / Merchant Banking" },
  { value: "fundraising", label: "Fundraising & Investor Relations" },
  { value: "finra-broker", label: "FINRA Broker / Registered Intermediary (Global)" },
  { value: "ma-advisory", label: "M&A Advisory & Deal Syndication" },
];

const industryOptions = [
  { value: "agriculture", label: "Agriculture & Agribusiness" },
  { value: "automotive", label: "Automotive & Auto Components" },
  { value: "bfsi", label: "Banking, Financial Services & Insurance (BFSI)" },
  { value: "consumer-goods", label: "Consumer Goods (FMCG, Retail, E-Commerce)" },
  { value: "education", label: "Education & Training" },
  { value: "energy", label: "Energy (Conventional & Renewable)" },
  { value: "healthcare", label: "Healthcare, Pharma & Biotechnology" },
  { value: "hospitality", label: "Hospitality, Travel & Leisure" },
  { value: "it", label: "Information Technology & Software Services" },
  { value: "logistics", label: "Logistics, Supply Chain & Transportation" },
  { value: "manufacturing", label: "Manufacturing & Industrial Products" },
  { value: "media", label: "Media, Entertainment & VFX" },
  { value: "mining", label: "Mining & Natural Resources" },
  { value: "professional", label: "Professional & Business Services" },
  { value: "real-estate", label: "Real Estate & Infrastructure" },
  { value: "telecom", label: "Telecom & Communications" },
  { value: "textiles", label: "Textiles, Apparel & Fashion" },
  { value: "utilities", label: "Utilities & Public Services" },
];

export default function ConsultantSignup() {
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;
  
  const [formData, setFormData] = useState({
    // Personal Info
    firstName: "",
    lastName: "",
    email: "",
    phoneCountryCode: "+91",
    phone: "",
    
    // Professional Info
    experience: "",
    consultantType: "",
    certifications: "",
    linkedinProfile: "",
    currentRole: "",
    organization: "",
    
    // Consulting Preferences
    consultingAreas: [] as string[],
    availability: "",
    preferredIndustries: [] as string[],
    preferredIndustriesOther: "",
    bio: ""
  });

  const updateFormData = (field: string, value: string | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = () => {
    // Handle form submission
    console.log("Consultant signup data:", formData);
    alert("Thank you for signing up! We'll review your application and get back to you within 24 hours.");
  };

  // Using shared constants for consultant types

  // Using shared constants for consulting areas and industries

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="consultant" />

      <div className="container mx-auto px-4 py-8 max-w-2xl">
        {/* Progress Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex justify-center mb-6">
            <Logo size="lg" />
          </div>
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-3xl font-bold text-black">Consultant Registration</h1>
            <span className="text-gray-600">Step {currentStep} of {totalSteps}</span>
          </div>
          <Progress value={(currentStep / totalSteps) * 100} className="h-2 mb-2" />
          <p className="text-gray-600 text-sm">Complete your profile to get started with 10xCFO</p>
        </motion.div>

        {/* Form */}
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="bg-white border-gray-200 shadow-lg">
            <CardHeader>
              <CardTitle className="text-2xl text-black">
                {currentStep === 1 && "Personal Information"}
                {currentStep === 2 && "Professional Background"}
                {currentStep === 3 && "Consulting Preferences"}
              </CardTitle>
              <CardDescription className="text-gray-600">
                {currentStep === 1 && "Tell us about yourself"}
                {currentStep === 2 && "Share your professional experience and expertise"}
                {currentStep === 3 && "Define your consulting preferences and availability"}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Step 1: Personal Information */}
              {currentStep === 1 && (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName" className="text-gray-700">First Name *</Label>
                      <Input
                        id="firstName"
                        value={formData.firstName}
                        onChange={(e) => updateFormData("firstName", e.target.value)}
                        className="bg-white border-gray-300 text-black"
                        placeholder="John"
                      />
                    </div>
                    <div>
                      <Label htmlFor="lastName" className="text-gray-700">Last Name *</Label>
                      <Input
                        id="lastName"
                        value={formData.lastName}
                        onChange={(e) => updateFormData("lastName", e.target.value)}
                        className="bg-white border-gray-300 text-black"
                        placeholder="Doe"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="email" className="text-gray-700">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => updateFormData("email", e.target.value)}
                      className="bg-white border-gray-300 text-black"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <Label htmlFor="phone" className="text-gray-700">Phone Number</Label>
                    <div className="flex gap-2">
                      <select
                        value={formData.phoneCountryCode}
                        onChange={(e) => updateFormData("phoneCountryCode", e.target.value)}
                        className="w-24 bg-white border border-gray-300 text-black rounded-md px-2 py-2"
                      >
                        {countries.map((country) => (
                          <option key={country.code} value={country.phoneCode}>
                            {country.phoneCode}
                          </option>
                        ))}
                      </select>
                      <Input
                        id="phone"
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => updateFormData("phone", e.target.value)}
                        className="flex-1 bg-white border-gray-300 text-black"
                        placeholder="98765 43210"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Step 2: Professional Background */}
              {currentStep === 2 && (
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="experience" className="text-gray-700">Years of Experience *</Label>
                    <select
                      id="experience"
                      value={formData.experience}
                      onChange={(e) => updateFormData("experience", e.target.value)}
                      className="w-full mt-1 bg-white border border-gray-300 text-black rounded-md px-3 py-2"
                    >
                      <option value="">Select experience level</option>
                      <option value="1-3">1-3 years</option>
                      <option value="3-5">3-5 years</option>
                      <option value="5-10">5-10 years</option>
                      <option value="10-15">10-15 years</option>
                      <option value="15+">15+ years</option>
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="consultantType" className="text-gray-700">Consultant Type *</Label>
                    <select
                      id="consultantType"
                      value={formData.consultantType}
                      onChange={(e) => updateFormData("consultantType", e.target.value)}
                      className="w-full mt-1 bg-white border border-gray-300 text-black rounded-md px-3 py-2"
                    >
                      <option value="">Select Type</option>
                      {consultantTypeOptions.map((option) => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <Label htmlFor="currentRole" className="text-gray-700">Current Role/Position</Label>
                    <Input
                      id="currentRole"
                      value={formData.currentRole}
                      onChange={(e) => updateFormData("currentRole", e.target.value)}
                      className="bg-white border-gray-300 text-black"
                      placeholder="Senior Business Analyst"
                    />
                  </div>

                  <div>
                    <Label htmlFor="organization" className="text-gray-700">Current Organization</Label>
                    <Input
                      id="organization"
                      value={formData.organization}
                      onChange={(e) => updateFormData("organization", e.target.value)}
                      className="bg-white border-gray-300 text-black"
                      placeholder="Company Name or Independent"
                    />
                  </div>

                  <div>
                    <Label htmlFor="linkedinProfile" className="text-gray-700">LinkedIn Profile</Label>
                    <Input
                      id="linkedinProfile"
                      value={formData.linkedinProfile}
                      onChange={(e) => updateFormData("linkedinProfile", e.target.value)}
                      className="bg-white border-gray-300 text-black"
                      placeholder="https://linkedin.com/in/yourprofile"
                    />
                  </div>
                </div>
              )}

              {/* Step 3: Consulting Preferences */}
              {currentStep === 3 && (
                <div className="space-y-4">
                  <div>
                    <Label className="text-gray-700 mb-3 block">Consulting Areas *</Label>
                    <MultiSelect
                      options={consultingAreaOptions}
                      selected={formData.consultingAreas}
                      onChange={(selected) => updateFormData("consultingAreas", selected)}
                      placeholder="Select consulting areas..."
                      className="bg-white border-gray-300 text-black"
                    />
                  </div>



                  <div>
                    <Label htmlFor="availability" className="text-gray-700">Availability</Label>
                    <select
                      id="availability"
                      value={formData.availability}
                      onChange={(e) => updateFormData("availability", e.target.value)}
                      className="w-full mt-1 bg-white border border-gray-300 text-black rounded-md px-3 py-2"
                    >
                      <option value="">Select availability</option>
                      <option value="part-time">Part-time (10-20 hours/week)</option>
                      <option value="full-time">Full-time (40+ hours/week)</option>
                      <option value="project-based">Project-based</option>
                      <option value="flexible">Flexible</option>
                    </select>
                  </div>

                  <div>
                    <Label className="text-gray-700 mb-3 block">Preferred Industries *</Label>
                    <MultiSelect
                      options={[...industryOptions, { value: "others", label: "Others (Open Field Entry)" }]}
                      selected={formData.preferredIndustries}
                      onChange={(selected) => updateFormData("preferredIndustries", selected)}
                      placeholder="Select preferred industries..."
                      className="bg-white border-gray-300 text-black"
                    />
                    {formData.preferredIndustries.includes("others") && (
                      <Input
                        className="mt-2 bg-white border-gray-300 text-black"
                        placeholder="Please specify other industries..."
                        onChange={(e) => updateFormData("preferredIndustriesOther", e.target.value)}
                      />
                    )}
                  </div>

                  <div>
                    <Label htmlFor="bio" className="text-gray-700">Professional Bio</Label>
                    <Textarea
                      id="bio"
                      value={formData.bio}
                      onChange={(e) => updateFormData("bio", e.target.value)}
                      className="bg-white border-gray-300 text-black"
                      placeholder="Brief description of your background and what you can offer to SMEs..."
                      rows={4}
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

        {/* Navigation Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex justify-between mt-8"
        >
          <div>
            {currentStep > 1 ? (
              <Button variant="outline" onClick={prevStep} className="border-gray-300 text-black hover:bg-gray-50">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Previous
              </Button>
            ) : (
              <Link href="/consultant">
                <Button variant="outline" className="border-gray-300 text-black hover:bg-gray-50">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Consultant Page
                </Button>
              </Link>
            )}
          </div>

          <div>
            {currentStep < totalSteps ? (
              <Button onClick={nextStep} className="bg-black hover:bg-gray-800 text-white">
                Next Step
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            ) : (
              <Button onClick={handleSubmit} className="bg-black hover:bg-gray-800 text-white">
                Submit Application
                <CheckCircle className="w-4 h-4 ml-2" />
              </Button>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  );
}

"use client";

import AppHeader from "@/components/navigation/AppHeader";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useAuthStore } from "@/stores/authStore";
import { motion } from "framer-motion";
import {
    ArrowRight,
    BarChart3,
    Building,
    Calendar,
    DollarSign,
    Eye,
    Filter,
    Heart,
    MapPin,
    MessageCircle,
    Search,
    TrendingUp
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function InvestorDashboard() {
  const router = useRouter();
  const { logout, user } = useAuthStore();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFilter, setSelectedFilter] = useState("all");

  const handleLogout = () => {
    logout();
    router.push('/');
  };

  // Mock data - Admin-shared content only
  const sharedReports = [
    {
      id: 1,
      companyName: "TechCorp Solutions",
      reportType: "Financial Health Score",
      sharedBy: "Admin",
      sharedDate: "2 days ago",
      industry: "Technology",
      description: "Comprehensive financial analysis and growth potential assessment"
    },
    {
      id: 2,
      companyName: "GreenEnergy Pvt Ltd",
      reportType: "Market Analysis",
      sharedBy: "Admin",
      sharedDate: "1 week ago",
      industry: "Renewable Energy",
      description: "Market opportunity analysis and competitive positioning"
    }
  ];

  const sharedData = [
    {
      id: 1,
      companyName: "TechCorp Solutions",
      dataType: "Financial Metrics",
      lastUpdated: "3 days ago",
      accessLevel: "Full Access"
    },
    {
      id: 2,
      companyName: "GreenEnergy Pvt Ltd",
      dataType: "Growth Metrics",
      lastUpdated: "1 week ago",
      accessLevel: "Limited Access"
    }
  ];

  const recentActivity = [
    { id: 1, type: "report", message: "Admin shared Financial Health Score for TechCorp Solutions", time: "2 days ago" },
    { id: 2, type: "data", message: "New data access granted for GreenEnergy Pvt Ltd", time: "1 week ago" },
    { id: 3, type: "report", message: "Admin shared Market Analysis report", time: "1 week ago" }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "new": return "bg-blue-100 text-blue-700 border-blue-200";
      case "trending": return "bg-green-100 text-green-700 border-green-200";
      case "hot": return "bg-red-100 text-red-700 border-red-200";
      default: return "bg-gray-100 text-gray-600 border-gray-200";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="investor" />

      <div className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="mb-4">
            <h1 className="text-3xl font-bold text-black mb-2">Welcome back, {user?.name || 'Investor'}</h1>
            <p className="text-gray-600">View reports and data shared by admin</p>
          </div>
        </motion.div>

        {/* Access Summary */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Eye className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
                <div className="text-3xl font-bold text-black mb-1">{sharedReports.length}</div>
                <p className="text-gray-600">Shared Reports</p>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <BarChart3 className="w-6 h-6 text-green-600" />
                  </div>
                </div>
                <div className="text-3xl font-bold text-black mb-1">{sharedData.length}</div>
                <p className="text-gray-600">Data Access Granted</p>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Shared Reports */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="lg:col-span-2"
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-black">Reports Shared by Admin</CardTitle>
                <CardDescription className="text-gray-600">
                  View and download reports that have been shared with you
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {sharedReports.map((report) => (
                    <div key={report.id} className="p-6 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h3 className="text-lg font-semibold text-black">{report.companyName}</h3>
                            <Badge className="bg-blue-100 text-blue-700 border-blue-200">
                              {report.industry}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{report.reportType}</p>
                          <p className="text-sm text-gray-700">{report.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                        <div className="text-sm text-gray-600">
                          <p>Shared by {report.sharedBy} • {report.sharedDate}</p>
                        </div>
                        <div className="flex space-x-2">
                          <Button size="sm" className="!bg-black !text-white">
                            <Eye className="w-4 h-4 mr-1" />
                            View Report
                          </Button>
                          <Button size="sm" variant="outline">
                            <ArrowRight className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                  {sharedReports.length === 0 && (
                    <div className="text-center py-12">
                      <Eye className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-black mb-2">No reports shared yet</h3>
                      <p className="text-gray-600">Admin will share reports with you as they become available</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Shared Data Access */}
            <Card className="bg-white border-gray-200 shadow-sm mt-8">
              <CardHeader>
                <CardTitle className="text-black">Data Access</CardTitle>
                <CardDescription className="text-gray-600">
                  View data that has been shared with you
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {sharedData.map((data) => (
                    <div key={data.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <h4 className="font-semibold text-black">{data.companyName}</h4>
                        <p className="text-sm text-gray-600">{data.dataType}</p>
                        <p className="text-xs text-gray-500">Updated {data.lastUpdated}</p>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Badge className="bg-green-100 text-green-700 border-green-200">
                          {data.accessLevel}
                        </Badge>
                        <Button size="sm" variant="outline">
                          <Eye className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Card className="bg-white border-gray-200 shadow-lg">
              <CardHeader>
                <CardTitle className="text-black flex items-center">
                  <Calendar className="w-5 h-5 mr-2 text-black" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3">
                      <div className={`w-2 h-2 rounded-full mt-2 ${
                        activity.type === "view" ? "bg-black" :
                        activity.type === "interest" ? "bg-black" :
                        activity.type === "message" ? "bg-black" : "bg-black"
                      }`} />
                      <div className="flex-1">
                        <p className="text-black text-sm">{activity.message}</p>
                        <p className="text-gray-600 text-xs">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}

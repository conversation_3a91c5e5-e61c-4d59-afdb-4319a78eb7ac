"use client";

import AppHeader from "@/components/navigation/AppHeader";
import Footer from "@/components/navigation/Footer";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import { ArrowRight, BarChart3, DollarSign, Search, Shield, Target, Users } from "lucide-react";
import Link from "next/link";

export default function InvestorOverview() {
  const benefits = [
    {
      icon: Search,
      title: "Curated Deal Pipeline",
      description: "Access pre-vetted, high-potential SMEs ready for investment.",
      color: "text-black",
      bgColor: "bg-gray-100",
    },
    {
      icon: Shield,
      title: "Risk Assessment Tools",
      description: "Comprehensive due diligence reports with AI-powered risk analysis and scoring.",
      color: "text-black",
      bgColor: "bg-gray-100",
    },
    {
      icon: BarChart3,
      title: "Performance Analytics",
      description: "Track portfolio performance and get insights on market trends and opportunities.",
      color: "text-black",
      bgColor: "bg-gray-100",
    },
    {
      icon: Users,
      title: "Direct SME Access",
      description: "Connect directly with business owners and schedule meetings without intermediaries.",
      color: "text-black",
      bgColor: "bg-gray-100",
    },
    {
      icon: Target,
      title: "Smart Matching",
      description: "Get matched with businesses that align with your investment criteria and preferences.",
      color: "text-black",
      bgColor: "bg-gray-100",
    },
    {
      icon: DollarSign,
      title: "Investment Tracking",
      description: "Monitor your investments, track returns, and manage your portfolio efficiently.",
      color: "text-black",
      bgColor: "bg-gray-100",
    },
  ];

  const steps = [
    {
      step: "1",
      title: "Set Investment Criteria",
      description: "Define your investment preferences, sectors, and risk appetite."
    },
    {
      step: "2",
      title: "Browse Opportunities",
      description: "Explore curated deals with detailed financial analysis and other due diligence reports."
    },
    {
      step: "3",
      title: "Evaluate & Connect",
      description: "Deal or data room access for conducting due diligence and connect to SME for evaluation"
    },
    {
      step: "4",
      title: "Invest & Track",
      description: "Make investments and monitor performance through our platform."
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="default" />

      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 px-4 sm:px-6 lg:px-8">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-100/20 to-gray-200/20" />
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-gray-200/20 via-transparent to-transparent" />
        
        <div className="relative max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Badge variant="secondary" className="mb-6 px-6 py-2 text-sm font-medium bg-gray-100 text-black border-gray-200">
              💰 For Investors
            </Badge>
            <h1 className="text-5xl sm:text-7xl font-bold text-black mb-8 leading-tight">
            Access investment opportunities
              <span className="block text-black">
              curated for you
              </span>
            </h1>
            <p className="text-body-large text-gray-600 mb-12 max-w-3xl mx-auto">
              Access a curated pipeline of pre-screened SMEs with comprehensive financial analysis, 
              risk assessment, and direct connection to business owners.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/investor/signup">
                <Button size="lg" className="px-8 py-4 text-button bg-black hover:bg-gray-800 text-white rounded-full">
                  Access Deal Pipeline <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
              <Link href="/auth/signin">
                <Button variant="outline" size="lg" className="px-8 py-4 text-button border-gray-300 text-black hover:bg-gray-50 rounded-full">
                  View Sample Deals
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-5xl font-bold mb-4" style={{ color: '#000000' }}>Why Investors Choose 10xCFO</h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg">
              Our platform provides everything you need to make informed investment decisions and connect with high-potential SMEs.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {benefits.map((benefit, index) => (
              <motion.div
                key={benefit.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="h-full bg-white border-gray-200 hover:border-gray-400 transition-all duration-300">
                  <CardContent className="p-8">
                    <div className={`w-16 h-16 ${benefit.bgColor} rounded-2xl flex items-center justify-center mb-6`}>
                      <benefit.icon className={`w-8 h-8 ${benefit.color}`} />
                    </div>
                    <h3 className="text-xl font-bold text-black mb-4">{benefit.title}</h3>
                    <p className="text-gray-600">
                      {benefit.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 px-4 bg-white">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-5xl font-bold mb-4" style={{ color: '#000000' }}>How It Works</h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg">
              Start investing in high-potential SMEs through these 4 easy steps
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-5xl mx-auto bg-white rounded-3xl py-12 px-8 shadow-lg border border-gray-100">
            {steps.map((step, index) => (
              <motion.div
                key={step.step}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="w-16 h-16 bg-black text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                  {step.step}
                </div>
                <h3 className="text-xl font-semibold text-black mb-2">{step.title}</h3>
                <p className="text-gray-600">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Investment Tiers - HIDDEN (We offer free access to investors which makes this redundant) */}
      {/*
      <section className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-5xl font-bold mb-4" style={{ color: '#000000' }}>Investment Access Tiers</h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg">
              Choose the access level that matches your investment capacity and interests.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <Card className="h-full bg-white border-gray-200 hover:border-gray-400 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <Search className="w-8 h-8 text-black" />
                  </div>
                  <h3 className="text-xl font-bold text-black mb-4">Explorer</h3>
                  <p className="text-gray-600 mb-6">
                    Perfect for new investors exploring deal opportunities.
                  </p>
                  <ul className="space-y-2 text-sm text-gray-600 mb-6">
                    <li>• Browse public deal summaries</li>
                    <li>• Basic financial health scores</li>
                    <li>• Industry trend reports</li>
                    <li>• Community access</li>
                  </ul>
                  <Button className="w-full mt-6 bg-black hover:bg-gray-800 text-white">
                    Get Started Free <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="h-full bg-white border-gray-300 hover:border-gray-400 transition-all duration-300 relative">
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-black text-white px-4 py-1">Most Popular</Badge>
                </div>
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <DollarSign className="w-8 h-8 text-black" />
                  </div>
                  <h3 className="text-xl font-bold text-black mb-4">Professional</h3>
                  <p className="text-gray-600 mb-6">
                    For active investors seeking comprehensive deal access and analytics.
                  </p>
                  <ul className="space-y-2 text-sm text-gray-600 mb-6">
                    <li>• Full deal access & details</li>
                    <li>• Direct SME introductions</li>
                    <li>• Advanced analytics & reports</li>
                    <li>• Investment tracking tools</li>
                    <li>• Priority support</li>
                  </ul>
                  <Button className="w-full mt-6 bg-black hover:bg-gray-800 text-white">
                    Start Free Trial <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Card className="h-full bg-white border-gray-200 hover:border-gray-400 transition-all duration-300">
                <CardContent className="p-8 text-center">
                  <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <Target className="w-8 h-8 text-black" />
                  </div>
                  <h3 className="text-xl font-bold text-black mb-4">Elite</h3>
                  <p className="text-gray-600 mb-6">
                    For institutional investors with exclusive access and dedicated support.
                  </p>
                  <ul className="space-y-2 text-sm text-gray-600 mb-6">
                    <li>• Everything in Professional</li>
                    <li>• Exclusive deal access</li>
                    <li>• Dedicated relationship manager</li>
                    <li>• Custom deal sourcing</li>
                    <li>• White-label reports</li>
                  </ul>
                  <Button className="w-full mt-6 bg-black hover:bg-gray-800 text-white">
                    Contact Sales <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>
      */}

      {/* CTA Section */}
      <section className="py-20 px-4 bg-black">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6 }}
          >
            <Badge className="mb-4 bg-white/20 text-white border-white/30">
              🎯 Ready to Invest?
            </Badge>
            <h2 className="text-4xl font-bold text-white mb-4">Discover. Invest. Grow</h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
              Join the growing community of investors finding and funding the best SMEs via 10xCFO
            </p>
            <div className="flex justify-center">
              <Link href="/investor/signup">
                <Button size="lg" className="text-lg px-8 py-4 rounded-full font-medium shadow-lg transition-all duration-300 !bg-white !text-black hover:!bg-gray-100">
                  Get Started <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

"use client";

import AppHeader from "@/components/navigation/AppHeader";
import Footer from "@/components/navigation/Footer";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import { ArrowRight, Check, Crown, Star, X, Zap } from "lucide-react";
import { useState } from "react";

export default function PricingPage() {
  const [billingCycle, setBillingCycle] = useState<"monthly" | "annual">("monthly");

  const plans = [
    {
      name: "Starter",
      description: "Perfect for getting started",
      monthlyPrice: 0,
      annualPrice: 0,
      icon: Zap,
      color: "blue",
      popular: false,
      features: [
        "Basic financial health score",
        "Document upload (up to 5 files)",
        "Basic report generation",
        "Email support",
        "Community access"
      ],
      limitations: [
        "Limited to 1 business profile",
        "Basic scoring algorithm",
        "No investor matching",
        "No advisor calls"
      ]
    },
    {
      name: "Professional",
      description: "For growing businesses",
      monthlyPrice: 39,
      annualPrice: 390,
      icon: Star,
      color: "emerald",
      popular: true,
      features: [
        "Advanced financial analysis",
        "Unlimited document uploads",
        "Detailed reports & insights",
        "Investor matching",
        "Priority support",
        "Benchmark comparisons",
        "Monthly advisor call",
        "API access"
      ],
      limitations: [
        "Up to 3 business profiles",
        "Standard investor network"
      ]
    },
    {
      name: "Enterprise",
      description: "For established businesses",
      monthlyPrice: 129,
      annualPrice: 1290,
      icon: Crown,
      color: "amber",
      popular: false,
      features: [
        "Everything in Professional",
        "10X Growth Hack access",
        "Dedicated advisor calls",
        "Custom integrations",
        "White-label reports",
        "Premium investor network",
        "24/7 phone support",
        "Custom onboarding",
        "Advanced analytics",
        "Multi-user accounts"
      ],
      limitations: []
    }
  ];

  const formatPrice = (price: number) => {
    if (price === 0) return "Free";
    return `$${price.toLocaleString()}`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="default" />

      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 px-4 sm:px-6 lg:px-8">
        <div className="absolute inset-0 bg-gradient-to-r from-gray-100/20 to-gray-200/20" />
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-gray-200/20 via-transparent to-transparent" />
        
        <div className="relative max-w-7xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Badge variant="secondary" className="mb-6 px-6 py-2 text-sm font-medium bg-gray-100 text-black border-gray-200">
              💰 Transparent Pricing
            </Badge>
            <h1 className="text-5xl sm:text-7xl font-bold text-black mb-8 leading-tight">
              Choose Your
              <span className="block text-black">
                Growth Plan
              </span>
            </h1>
            <p className="text-body-large text-gray-600 mb-12 max-w-3xl mx-auto">
              Start free and scale as you grow. All plans include our core financial evaluation features 
              with no hidden fees or long-term contracts.
            </p>

            {/* Billing Toggle */}
            <div className="flex items-center justify-center mb-12">
              <div className="bg-white rounded-full p-1 border border-gray-300 shadow-sm">
                <button
                  onClick={() => setBillingCycle("monthly")}
                  className={`px-6 py-2 rounded-full text-sm font-medium transition-all ${
                    billingCycle === "monthly"
                      ? "bg-black text-white"
                      : "text-gray-600 hover:text-black"
                  }`}
                >
                  Monthly
                </button>
                <button
                  onClick={() => setBillingCycle("annual")}
                  className={`px-6 py-2 rounded-full text-sm font-medium transition-all ${
                    billingCycle === "annual"
                      ? "bg-black text-white"
                      : "text-gray-600 hover:text-black"
                  }`}
                >
                  Annual
                  <Badge className="ml-2 bg-gray-100 text-black text-xs">Save 17%</Badge>
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="py-20 px-4 bg-gray-50">
        <div className="container mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {plans.map((plan, index) => (
              <motion.div
                key={plan.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="relative"
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 z-10">
                    <Badge className="bg-black text-white px-4 py-1">Most Popular</Badge>
                  </div>
                )}
                <Card className={`h-full ${
                  plan.popular 
                    ? "bg-white border-gray-300 scale-105" 
                    : "bg-white border-gray-200 hover:border-gray-400"
                } transition-all duration-300`}>
                  <CardContent className="p-8">
                    <div className="text-center mb-8">
                      <div className="w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <plan.icon className="w-8 h-8 text-black" />
                      </div>
                      <h3 className="text-2xl font-bold text-black mb-2">{plan.name}</h3>
                      <p className="text-gray-600 mb-4">{plan.description}</p>
                      <div className="text-4xl font-bold text-black mb-2">
                        {formatPrice(billingCycle === "monthly" ? plan.monthlyPrice : plan.annualPrice)}
                        {plan.monthlyPrice > 0 && (
                          <span className="text-lg text-gray-600">
                            /{billingCycle === "monthly" ? "month" : "year"}
                          </span>
                        )}
                      </div>
                      {billingCycle === "annual" && plan.monthlyPrice > 0 && (
                        <p className="text-sm text-gray-600">
                          ${Math.round(plan.annualPrice / 12).toLocaleString()}/month billed annually
                        </p>
                      )}
                    </div>

                    <ul className="space-y-3 mb-8">
                      {plan.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start text-gray-700">
                          <Check className="w-5 h-5 text-black mr-3 mt-0.5 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                      {plan.limitations.map((limitation, limitationIndex) => (
                        <li key={limitationIndex} className="flex items-start text-gray-600">
                          <X className="w-5 h-5 text-gray-500 mr-3 mt-0.5 flex-shrink-0" />
                          {limitation}
                        </li>
                      ))}
                    </ul>

                    <Button className="w-full bg-black hover:bg-gray-800 text-white">
                      {plan.name === "Starter" ? "Get Started Free" : 
                       plan.name === "Enterprise" ? "Contact Sales" : "Start Free Trial"}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 px-4 bg-white">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-5xl font-bold mb-4" style={{ color: '#000000' }}>Frequently Asked Questions</h2>
            <p className="text-gray-600 max-w-2xl mx-auto text-lg">
              Everything you need to know about our pricing and plans.
            </p>
          </motion.div>

          <div className="max-w-3xl mx-auto space-y-6">
            {[
              {
                question: "Can I change plans anytime?",
                answer: "Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately and we'll prorate the billing."
              },
              {
                question: "Is there a free trial?",
                answer: "Yes! Professional and Enterprise plans come with a 14-day free trial. No credit card required to start."
              },
              {
                question: "What payment methods do you accept?",
                answer: "We accept all major credit cards, UPI, net banking, and bank transfers for annual plans."
              },
              {
                question: "Is my data secure?",
                answer: "Absolutely. We use bank-grade encryption and security measures to protect your sensitive business data."
              }
            ].map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="bg-white border-gray-200">
                  <CardContent className="p-6">
                    <h3 className="text-xl font-bold text-black mb-2">{faq.question}</h3>
                    <p className="text-gray-600">{faq.answer}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}

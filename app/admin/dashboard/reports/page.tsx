"use client";

import AppHeader from "@/components/navigation/AppHeader";
import BackButton from "@/components/navigation/BackButton";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { motion } from "framer-motion";
import { 
  FileText, 
  Search, 
  ExternalLink,
  Share2,
  Play,
  CheckCircle,
  Clock,
  AlertCircle
} from "lucide-react";
import { useState } from "react";

interface ReportRequest {
  id: number;
  smeCompany: string;
  smeContact: string;
  reportType: string;
  requestDate: string;
  status: "pending" | "processing" | "completed" | "shared";
  priority: "high" | "medium" | "low";
  sharedWith: string[];
}

export default function AdminReportManagement() {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [selectedReport, setSelectedReport] = useState<ReportRequest | null>(null);

  // Mock report requests data
  const reportRequests: ReportRequest[] = [
    {
      id: 1,
      smeCompany: "TechCorp Solutions",
      smeContact: "<EMAIL>",
      reportType: "Financial Health Score",
      requestDate: "2024-02-15",
      status: "pending",
      priority: "high",
      sharedWith: []
    },
    {
      id: 2,
      smeCompany: "GreenEnergy Ltd",
      smeContact: "<EMAIL>",
      reportType: "Growth Analysis",
      requestDate: "2024-02-14",
      status: "processing",
      priority: "medium",
      sharedWith: []
    },
    {
      id: 3,
      smeCompany: "HealthTech Innovations",
      smeContact: "<EMAIL>",
      reportType: "Market Analysis",
      requestDate: "2024-02-10",
      status: "completed",
      priority: "medium",
      sharedWith: ["Investment Fund LLC", "Venture Capital Partners"]
    },
    {
      id: 4,
      smeCompany: "RetailCorp",
      smeContact: "<EMAIL>",
      reportType: "Financial Health Score",
      requestDate: "2024-02-12",
      status: "shared",
      priority: "low",
      sharedWith: ["Angel Investors Group"]
    }
  ];

  const getStatusBadge = (status: string) => {
    const colors = {
      pending: "bg-yellow-100 text-yellow-700 border-yellow-200",
      processing: "bg-blue-100 text-blue-700 border-blue-200",
      completed: "bg-green-100 text-green-700 border-green-200",
      shared: "bg-purple-100 text-purple-700 border-purple-200"
    };
    return <Badge className={colors[status as keyof typeof colors]}>{status}</Badge>;
  };

  const getPriorityBadge = (priority: string) => {
    const colors = {
      high: "bg-red-100 text-red-700 border-red-200",
      medium: "bg-orange-100 text-orange-700 border-orange-200",
      low: "bg-gray-100 text-gray-700 border-gray-200"
    };
    return <Badge className={colors[priority as keyof typeof colors]}>{priority}</Badge>;
  };

  const filteredReports = reportRequests.filter(report => {
    const matchesSearch = report.smeCompany.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.reportType.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === "all" || report.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="default" />
      <BackButton href="/admin/dashboard" label="Back to Dashboard" />

      <div className="container mx-auto px-6 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-2">
            <FileText className="w-8 h-8 text-black" />
            <h1 className="text-3xl font-bold text-black">Report Management</h1>
          </div>
          <p className="text-gray-600">Execute reports and manage data sharing with investors</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-white border-gray-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm">Pending</p>
                  <p className="text-2xl font-bold text-black">
                    {reportRequests.filter(r => r.status === "pending").length}
                  </p>
                </div>
                <Clock className="w-8 h-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white border-gray-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm">Processing</p>
                  <p className="text-2xl font-bold text-black">
                    {reportRequests.filter(r => r.status === "processing").length}
                  </p>
                </div>
                <Play className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white border-gray-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm">Completed</p>
                  <p className="text-2xl font-bold text-black">
                    {reportRequests.filter(r => r.status === "completed").length}
                  </p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white border-gray-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-600 text-sm">Shared</p>
                  <p className="text-2xl font-bold text-black">
                    {reportRequests.filter(r => r.status === "shared").length}
                  </p>
                </div>
                <Share2 className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="bg-white border-gray-200 shadow-sm mb-6">
          <CardContent className="p-6">
            <div className="flex gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-4 h-4" />
                <Input
                  placeholder="Search reports..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-white border-gray-300 text-black"
                />
              </div>
              <div className="flex gap-2">
                {["all", "pending", "processing", "completed", "shared"].map((status) => (
                  <Button
                    key={status}
                    variant={filterStatus === status ? "default" : "outline"}
                    size="sm"
                    onClick={() => setFilterStatus(status)}
                    className={filterStatus === status ? "!bg-black !text-white" : ""}
                  >
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Reports List */}
        <Card className="bg-white border-gray-200 shadow-sm">
          <CardHeader>
            <CardTitle className="text-black">
              Report Requests ({filteredReports.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredReports.map((report, index) => (
                <motion.div
                  key={report.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  className="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="font-semibold text-black">{report.smeCompany}</h4>
                        {getStatusBadge(report.status)}
                        {getPriorityBadge(report.priority)}
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-600 mb-3">
                        <div>
                          <span className="font-medium">Report Type:</span> {report.reportType}
                        </div>
                        <div>
                          <span className="font-medium">Contact:</span> {report.smeContact}
                        </div>
                        <div>
                          <span className="font-medium">Requested:</span> {report.requestDate}
                        </div>
                      </div>
                      {report.sharedWith.length > 0 && (
                        <div className="text-sm text-gray-600">
                          <span className="font-medium">Shared with:</span> {report.sharedWith.join(", ")}
                        </div>
                      )}
                    </div>
                    <div className="flex gap-2 ml-4">
                      {report.status === "pending" && (
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button size="sm" className="!bg-black !text-white">
                              <Play className="w-4 h-4 mr-1" />
                              Execute Report
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Execute Report</DialogTitle>
                              <DialogDescription>
                                Run report via external platform integration
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-4 py-4">
                              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <p className="text-sm text-blue-800">
                                  <strong>Company:</strong> {report.smeCompany}
                                </p>
                                <p className="text-sm text-blue-800">
                                  <strong>Report Type:</strong> {report.reportType}
                                </p>
                              </div>
                              <div className="space-y-2">
                                <Label>External Platform URL</Label>
                                <Input placeholder="https://external-platform.com/api/reports" />
                              </div>
                              <div className="space-y-2">
                                <Label>API Key</Label>
                                <Input type="password" placeholder="Enter API key" />
                              </div>
                              <Button className="w-full !bg-black !text-white">
                                <ExternalLink className="w-4 h-4 mr-2" />
                                Execute via External Platform
                              </Button>
                            </div>
                          </DialogContent>
                        </Dialog>
                      )}
                      {(report.status === "completed" || report.status === "shared") && (
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button size="sm" variant="outline">
                              <Share2 className="w-4 h-4 mr-1" />
                              Share with Investors
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Share Report with Investors</DialogTitle>
                              <DialogDescription>
                                Select investors to share this report with
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-4 py-4">
                              <div className="space-y-2">
                                <Label>Select Investors</Label>
                                <div className="space-y-2 max-h-60 overflow-y-auto">
                                  {["Investment Fund LLC", "Venture Capital Partners", "Angel Investors Group", "Growth Equity Fund"].map((investor) => (
                                    <div key={investor} className="flex items-center space-x-2">
                                      <input
                                        type="checkbox"
                                        id={investor}
                                        className="h-4 w-4 rounded border-gray-300"
                                        defaultChecked={report.sharedWith.includes(investor)}
                                      />
                                      <label htmlFor={investor} className="text-sm text-gray-700">
                                        {investor}
                                      </label>
                                    </div>
                                  ))}
                                </div>
                              </div>
                              <Button className="w-full !bg-black !text-white">
                                Update Sharing Permissions
                              </Button>
                            </div>
                          </DialogContent>
                        </Dialog>
                      )}
                      {report.status === "processing" && (
                        <Button size="sm" variant="outline" disabled>
                          <Clock className="w-4 h-4 mr-1" />
                          Processing...
                        </Button>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {filteredReports.length === 0 && (
              <div className="text-center py-12">
                <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-black mb-2">No reports found</h3>
                <p className="text-gray-600">Try adjusting your search or filter criteria</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

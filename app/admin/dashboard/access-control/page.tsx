"use client";

import AppHeader from "@/components/navigation/AppHeader";
import BackButton from "@/components/navigation/BackButton";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { motion } from "framer-motion";
import { Shield, Eye, EyeOff, CheckCircle, XCircle } from "lucide-react";
import { useState } from "react";

interface InvestorAccess {
  id: number;
  investorName: string;
  company: string;
  accessLevel: "full" | "limited" | "restricted";
  allowedReports: string[];
  allowedData: string[];
}

export default function AdminAccessControl() {
  const [investors, setInvestors] = useState<InvestorAccess[]>([
    {
      id: 1,
      investorName: "<PERSON> Smith",
      company: "Investment Fund LLC",
      accessLevel: "full",
      allowedReports: ["Financial Health Score", "Growth Analysis", "Market Analysis"],
      allowedData: ["Financial Metrics", "Growth Metrics", "Market Data"]
    },
    {
      id: 2,
      investorName: "<PERSON> Chen",
      company: "Venture Capital Partners",
      accessLevel: "limited",
      allowedReports: ["Financial Health Score"],
      allowedData: ["Financial Metrics"]
    },
    {
      id: 3,
      investorName: "Robert Davis",
      company: "Angel Investors Group",
      accessLevel: "restricted",
      allowedReports: [],
      allowedData: []
    }
  ]);

  const allReportTypes = [
    "Financial Health Score",
    "Growth Analysis",
    "Market Analysis",
    "Risk Assessment",
    "Valuation Report"
  ];

  const allDataTypes = [
    "Financial Metrics",
    "Growth Metrics",
    "Market Data",
    "Customer Data",
    "Operational Data"
  ];

  const toggleReportAccess = (investorId: number, report: string) => {
    setInvestors(investors.map(inv => {
      if (inv.id === investorId) {
        const hasAccess = inv.allowedReports.includes(report);
        return {
          ...inv,
          allowedReports: hasAccess 
            ? inv.allowedReports.filter(r => r !== report)
            : [...inv.allowedReports, report]
        };
      }
      return inv;
    }));
  };

  const toggleDataAccess = (investorId: number, data: string) => {
    setInvestors(investors.map(inv => {
      if (inv.id === investorId) {
        const hasAccess = inv.allowedData.includes(data);
        return {
          ...inv,
          allowedData: hasAccess 
            ? inv.allowedData.filter(d => d !== data)
            : [...inv.allowedData, data]
        };
      }
      return inv;
    }));
  };

  const getAccessLevelBadge = (level: string) => {
    const colors = {
      full: "bg-green-100 text-green-700 border-green-200",
      limited: "bg-yellow-100 text-yellow-700 border-yellow-200",
      restricted: "bg-red-100 text-red-700 border-red-200"
    };
    return <Badge className={colors[level as keyof typeof colors]}>{level}</Badge>;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="default" />
      <BackButton href="/admin/dashboard" label="Back to Dashboard" />

      <div className="container mx-auto px-6 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-2">
            <Shield className="w-8 h-8 text-black" />
            <h1 className="text-3xl font-bold text-black">Access Control</h1>
          </div>
          <p className="text-gray-600">Manage granular permissions for investor data access</p>
        </div>

        {/* Investors Access Control */}
        <div className="space-y-6">
          {investors.map((investor, index) => (
            <motion.div
              key={investor.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <Card className="bg-white border-gray-200 shadow-sm">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-black">{investor.investorName}</CardTitle>
                      <p className="text-sm text-gray-600">{investor.company}</p>
                    </div>
                    {getAccessLevelBadge(investor.accessLevel)}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Report Access */}
                    <div>
                      <h4 className="font-semibold text-black mb-3 flex items-center">
                        <Eye className="w-4 h-4 mr-2" />
                        Report Access
                      </h4>
                      <div className="space-y-2">
                        {allReportTypes.map((report) => {
                          const hasAccess = investor.allowedReports.includes(report);
                          return (
                            <div
                              key={report}
                              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                            >
                              <span className="text-sm text-gray-700">{report}</span>
                              <button
                                onClick={() => toggleReportAccess(investor.id, report)}
                                className={`p-1 rounded ${
                                  hasAccess 
                                    ? "text-green-600 hover:text-green-700" 
                                    : "text-gray-400 hover:text-gray-500"
                                }`}
                              >
                                {hasAccess ? (
                                  <CheckCircle className="w-5 h-5" />
                                ) : (
                                  <XCircle className="w-5 h-5" />
                                )}
                              </button>
                            </div>
                          );
                        })}
                      </div>
                    </div>

                    {/* Data Access */}
                    <div>
                      <h4 className="font-semibold text-black mb-3 flex items-center">
                        <Shield className="w-4 h-4 mr-2" />
                        Data Access
                      </h4>
                      <div className="space-y-2">
                        {allDataTypes.map((data) => {
                          const hasAccess = investor.allowedData.includes(data);
                          return (
                            <div
                              key={data}
                              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                            >
                              <span className="text-sm text-gray-700">{data}</span>
                              <button
                                onClick={() => toggleDataAccess(investor.id, data)}
                                className={`p-1 rounded ${
                                  hasAccess 
                                    ? "text-green-600 hover:text-green-700" 
                                    : "text-gray-400 hover:text-gray-500"
                                }`}
                              >
                                {hasAccess ? (
                                  <CheckCircle className="w-5 h-5" />
                                ) : (
                                  <XCircle className="w-5 h-5" />
                                )}
                              </button>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 pt-6 border-t border-gray-200">
                    <div className="flex justify-between items-center">
                      <div className="text-sm text-gray-600">
                        <span className="font-medium">Access Summary:</span> {investor.allowedReports.length} reports, {investor.allowedData.length} data types
                      </div>
                      <Button size="sm" className="!bg-black !text-white">
                        Save Changes
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
}

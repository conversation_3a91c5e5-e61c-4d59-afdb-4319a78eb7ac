"use client";

import AppHeader from "@/components/navigation/AppHeader";
import BackButton from "@/components/navigation/BackButton";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { motion } from "framer-motion";
import { 
  Award, 
  BarChart3, 
  BookOpen, 
  Calendar, 
  CheckCircle, 
  Clock, 
  DollarSign, 
  Download, 
  Play, 
  Star, 
  Target, 
  TrendingUp, 
  Users, 
  Video 
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface Module {
  id: number;
  title: string;
  description: string;
  duration: string;
  status: "completed" | "current" | "locked";
  progress: number;
  lessons: number;
  completedLessons: number;
}

interface Milestone {
  id: number;
  title: string;
  description: string;
  target: string;
  current: string;
  progress: number;
  status: "completed" | "in-progress" | "pending";
}

export default function SME10xGrowth() {
  const [activeTab, setActiveTab] = useState("overview");

  // Mock data - replace with actual API calls
  const userProgress = {
    overallProgress: 65,
    completedModules: 4,
    totalModules: 8,
    currentStreak: 12,
    totalPoints: 2450,
    nextMilestone: "Revenue Growth Target"
  };

  const modules: Module[] = [
    {
      id: 1,
      title: "Financial Foundation",
      description: "Master the fundamentals of business finance and cash flow management",
      duration: "2 weeks",
      status: "completed",
      progress: 100,
      lessons: 8,
      completedLessons: 8
    },
    {
      id: 2,
      title: "Market Analysis & Strategy",
      description: "Learn to analyze your market and develop winning strategies",
      duration: "3 weeks",
      status: "completed",
      progress: 100,
      lessons: 12,
      completedLessons: 12
    },
    {
      id: 3,
      title: "Digital Marketing Mastery",
      description: "Scale your business with effective digital marketing techniques",
      duration: "4 weeks",
      status: "current",
      progress: 75,
      lessons: 15,
      completedLessons: 11
    },
    {
      id: 4,
      title: "Operations Optimization",
      description: "Streamline operations for maximum efficiency and growth",
      duration: "3 weeks",
      status: "locked",
      progress: 0,
      lessons: 10,
      completedLessons: 0
    }
  ];

  const milestones: Milestone[] = [
    {
      id: 1,
      title: "Revenue Growth",
      description: "Achieve 25% revenue increase",
      target: "$500K",
      current: "$380K",
      progress: 76,
      status: "in-progress"
    },
    {
      id: 2,
      title: "Customer Acquisition",
      description: "Reach 1000 active customers",
      target: "1000",
      current: "750",
      progress: 75,
      status: "in-progress"
    },
    {
      id: 3,
      title: "Market Expansion",
      description: "Enter 2 new market segments",
      target: "2",
      current: "1",
      progress: 50,
      status: "in-progress"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "bg-green-500/20 text-green-400 border-green-500/30";
      case "current": case "in-progress": return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      case "locked": case "pending": return "bg-slate-500/20 text-slate-400 border-slate-500/30";
      default: return "bg-slate-500/20 text-slate-400 border-slate-500/30";
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <AppHeader variant="sme" />
      
      {/* Back Navigation */}
      <BackButton href="/sme/dashboard" label="Back to Dashboard" />

      <div className="container mx-auto px-6 py-8">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white mb-2">10X Growth Program</h1>
            <p className="text-slate-400">Accelerate your business growth with our comprehensive program</p>
          </div>
          <Badge className="bg-gradient-to-r from-yellow-500/20 to-orange-500/20 text-yellow-400 border-yellow-500/30">
            <Star className="w-4 h-4 mr-1" />
            Premium Member
          </Badge>
        </div>

        {/* Progress Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <Card className="bg-gradient-to-r from-blue-900/50 to-purple-900/50 border-blue-500/30">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1">{userProgress.overallProgress}%</div>
                  <div className="text-slate-300 text-sm">Overall Progress</div>
                  <Progress value={userProgress.overallProgress} className="mt-2 h-2" />
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1">{userProgress.completedModules}/{userProgress.totalModules}</div>
                  <div className="text-slate-300 text-sm">Modules Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1">{userProgress.currentStreak}</div>
                  <div className="text-slate-300 text-sm">Day Streak</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-white mb-1">{userProgress.totalPoints}</div>
                  <div className="text-slate-300 text-sm">Points Earned</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 mb-8 bg-slate-800/50 rounded-lg p-1">
          {[
            { id: "overview", label: "Overview", icon: BarChart3 },
            { id: "modules", label: "Learning Modules", icon: BookOpen },
            { id: "milestones", label: "Milestones", icon: Target },
            { id: "resources", label: "Resources", icon: Download }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors flex-1 justify-center ${
                activeTab === tab.id
                  ? "bg-blue-600 text-white"
                  : "text-slate-300 hover:text-white hover:bg-slate-700/50"
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span className="text-sm font-medium">{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Tab Content */}
        {activeTab === "overview" && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Current Focus */}
            <div className="lg:col-span-2">
              <Card className="bg-slate-800/50 border-slate-700 mb-6">
                <CardHeader>
                  <CardTitle className="text-white flex items-center">
                    <Play className="w-5 h-5 mr-2" />
                    Continue Learning
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="bg-slate-700/50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="text-white font-semibold">Digital Marketing Mastery</h3>
                      <Badge className={getStatusColor("current")}>In Progress</Badge>
                    </div>
                    <p className="text-slate-400 text-sm mb-4">
                      Learn advanced digital marketing strategies to scale your customer acquisition
                    </p>
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-slate-300 text-sm">Progress: 11/15 lessons</span>
                      <span className="text-slate-300 text-sm">75% complete</span>
                    </div>
                    <Progress value={75} className="mb-4 h-2" />
                    <Button className="w-full">Continue Module</Button>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Stats */}
              <div className="grid grid-cols-2 gap-4">
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                        <TrendingUp className="w-5 h-5 text-green-400" />
                      </div>
                      <div>
                        <div className="text-white font-semibold">Revenue Growth</div>
                        <div className="text-green-400 text-sm">+23% this quarter</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                        <Users className="w-5 h-5 text-blue-400" />
                      </div>
                      <div>
                        <div className="text-white font-semibold">New Customers</div>
                        <div className="text-blue-400 text-sm">+156 this month</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Next Milestone */}
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white text-lg">Next Milestone</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
                      <Award className="w-8 h-8 text-yellow-400" />
                    </div>
                    <h3 className="text-white font-semibold mb-2">{userProgress.nextMilestone}</h3>
                    <p className="text-slate-400 text-sm mb-4">Complete 2 more modules to unlock</p>
                    <Progress value={75} className="h-2" />
                  </div>
                </CardContent>
              </Card>

              {/* Upcoming Sessions */}
              <Card className="bg-slate-800/50 border-slate-700">
                <CardHeader>
                  <CardTitle className="text-white text-lg">Upcoming Sessions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="bg-slate-700/50 rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-2">
                        <Video className="w-4 h-4 text-blue-400" />
                        <span className="text-white text-sm font-medium">1-on-1 Coaching</span>
                      </div>
                      <div className="text-slate-400 text-xs">Tomorrow, 2:00 PM</div>
                    </div>
                    <div className="bg-slate-700/50 rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-2">
                        <Users className="w-4 h-4 text-green-400" />
                        <span className="text-white text-sm font-medium">Group Workshop</span>
                      </div>
                      <div className="text-slate-400 text-xs">Friday, 10:00 AM</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {activeTab === "modules" && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {modules.map((module, index) => (
              <motion.div
                key={module.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="bg-slate-800/50 border-slate-700 hover:border-slate-600 transition-colors">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-white text-lg">{module.title}</CardTitle>
                        <p className="text-slate-400 text-sm mt-1">{module.description}</p>
                      </div>
                      <Badge className={getStatusColor(module.status)}>
                        {module.status === "current" ? "In Progress" : module.status}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-slate-400">Progress</span>
                        <span className="text-slate-300">{module.completedLessons}/{module.lessons} lessons</span>
                      </div>
                      <Progress value={module.progress} className="h-2" />
                      
                      <div className="flex items-center justify-between text-sm">
                        <div className="flex items-center space-x-2">
                          <Clock className="w-4 h-4 text-slate-400" />
                          <span className="text-slate-400">{module.duration}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <BookOpen className="w-4 h-4 text-slate-400" />
                          <span className="text-slate-400">{module.lessons} lessons</span>
                        </div>
                      </div>

                      <Button 
                        className="w-full" 
                        disabled={module.status === "locked"}
                        variant={module.status === "locked" ? "outline" : "default"}
                      >
                        {module.status === "completed" ? "Review Module" : 
                         module.status === "current" ? "Continue Module" : 
                         "Locked"}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        )}

        {activeTab === "milestones" && (
          <div className="space-y-6">
            {milestones.map((milestone, index) => (
              <motion.div
                key={milestone.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="bg-slate-800/50 border-slate-700">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h3 className="text-white font-semibold text-lg">{milestone.title}</h3>
                        <p className="text-slate-400">{milestone.description}</p>
                      </div>
                      <Badge className={getStatusColor(milestone.status)}>
                        {milestone.status === "in-progress" ? "In Progress" : milestone.status}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-4 mb-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-white">{milestone.current}</div>
                        <div className="text-slate-400 text-sm">Current</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-400">{milestone.progress}%</div>
                        <div className="text-slate-400 text-sm">Progress</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-400">{milestone.target}</div>
                        <div className="text-slate-400 text-sm">Target</div>
                      </div>
                    </div>
                    
                    <Progress value={milestone.progress} className="h-3" />
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        )}

        {activeTab === "resources" && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              { title: "Growth Playbook", type: "PDF", size: "2.4 MB", downloads: 1250 },
              { title: "Financial Templates", type: "Excel", size: "1.8 MB", downloads: 980 },
              { title: "Marketing Toolkit", type: "ZIP", size: "15.2 MB", downloads: 750 },
              { title: "Case Studies", type: "PDF", size: "5.1 MB", downloads: 650 },
              { title: "Video Tutorials", type: "MP4", size: "120 MB", downloads: 420 },
              { title: "Checklists & Forms", type: "PDF", size: "3.2 MB", downloads: 890 }
            ].map((resource, index) => (
              <motion.div
                key={resource.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="bg-slate-800/50 border-slate-700 hover:border-slate-600 transition-colors">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                        <Download className="w-6 h-6 text-blue-400" />
                      </div>
                      <Badge variant="outline" className="border-slate-600 text-slate-300">
                        {resource.type}
                      </Badge>
                    </div>
                    <h3 className="text-white font-semibold mb-2">{resource.title}</h3>
                    <div className="text-slate-400 text-sm mb-4">
                      <div>Size: {resource.size}</div>
                      <div>Downloads: {resource.downloads}</div>
                    </div>
                    <Button className="w-full" size="sm">
                      <Download className="w-4 h-4 mr-2" />
                      Download
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

# TenxCFO Development Guide

This guide provides detailed instructions for setting up and developing the TenxCFO platform.

## 🏗️ Architecture Overview

The TenxCFO platform is a full-stack application with the following components:

- **Frontend**: Next.js 15 with TypeScript, Tailwind CSS, and Zustand
- **Backend**: Node.js/Express API with TypeScript
- **Database**: PostgreSQL 15 with <PERSON>is for caching
- **Infrastructure**: Docker Compose for development and production

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

1. **Docker Desktop** (v4.0+)
   - [Download for macOS](https://docs.docker.com/desktop/install/mac-install/)
   - [Download for Windows](https://docs.docker.com/desktop/install/windows-install/)
   - [Download for Linux](https://docs.docker.com/desktop/install/linux-install/)

2. **Git** (for version control)

3. **Make** (optional, for convenience commands)
   - macOS: `xcode-select --install`
   - Windows: Install via Chocolatey or WSL
   - Linux: Usually pre-installed

## 🚀 Quick Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd tenxcfo-latest
```

### 2. Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your preferences (optional for development)
# The default values work for local development
```

### 3. Start Development Environment

Choose one of the following methods:

#### Option A: Using Setup Script (Recommended)
```bash
./setup.sh
```

#### Option B: Using Make Commands
```bash
make setup
```

#### Option C: Using Docker Compose Directly
```bash
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
```

### 4. Verify Installation

Once the services are running, verify everything is working:

```bash
# Check service status
make status

# Check health endpoints
curl http://localhost:3000  # Frontend
curl http://localhost:4000/api/health  # Backend API
```

## 🌐 Access Points

After successful setup, you can access:

- **Frontend Application**: http://localhost:3000
- **Backend API**: http://localhost:4000
- **API Documentation**: http://localhost:4000/api/health
- **Database**: localhost:5432
  - Database: `tenxcfo`
  - Username: `tenxcfo_user`
  - Password: `tenxcfo_password`
- **Redis**: localhost:6379

## 👥 Test Accounts

Use these credentials to test different user roles:

| Role | Email | Password |
|------|-------|----------|
| **Admin** | `<EMAIL>` | `password123` |
| **SME** | `<EMAIL>` | `password123` |
| **Consultant** | `<EMAIL>` | `password123` |
| **Investor** | `<EMAIL>` | `password123` |

## 🛠️ Development Workflow

### Daily Development

1. **Start services:**
   ```bash
   make start
   ```

2. **View logs:**
   ```bash
   make logs           # All services
   make logs-frontend  # Frontend only
   make logs-backend   # Backend only
   ```

3. **Make code changes:**
   - Frontend changes in `frontend/` auto-reload
   - Backend changes in `backend/src/` auto-restart

4. **Stop services:**
   ```bash
   make stop
   ```

### Database Operations

```bash
# Access database shell
make shell-db

# Reset database (drops and recreates)
make reset-db

# Run migrations manually
make shell-backend
npm run db:migrate

# Seed database manually
make shell-backend
npm run db:seed
```

### Container Operations

```bash
# Access container shells
make shell-frontend  # Frontend container
make shell-backend   # Backend container

# Rebuild containers
make build
make build-no-cache  # Force rebuild

# View container status
make status
```

## 🧪 Testing

### Frontend Testing
```bash
make test-frontend
make lint-frontend
```

### Backend Testing
```bash
make test-backend
make lint-backend
```

### Code Formatting
```bash
make format  # Format both frontend and backend
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Port Conflicts
```bash
# Check what's using the ports
lsof -i :3000  # Frontend
lsof -i :4000  # Backend
lsof -i :5432  # PostgreSQL
lsof -i :6379  # Redis

# Kill processes if needed
sudo kill -9 <PID>
```

#### 2. Docker Issues
```bash
# Clean everything and start fresh
make clean
make setup

# Or manually
docker-compose down -v
docker system prune -f
```

#### 3. Database Connection Issues
```bash
# Check database status
make status

# View database logs
make logs-db

# Reset database
make reset-db
```

#### 4. Build Failures
```bash
# Rebuild without cache
make build-no-cache

# Check for syntax errors
make lint-frontend
make lint-backend
```

### Health Checks

```bash
# Check all service health
make health

# Individual service checks
curl http://localhost:3000
curl http://localhost:4000/api/health
```

## 📁 Project Structure

```
tenxcfo-latest/
├── frontend/                 # Next.js application
│   ├── app/                 # Next.js app router
│   ├── components/          # React components
│   ├── lib/                 # Utilities
│   ├── stores/              # Zustand state
│   └── Dockerfile
├── backend/                  # Express.js API
│   ├── src/
│   │   ├── controllers/     # Route handlers
│   │   ├── middleware/      # Express middleware
│   │   ├── routes/          # API routes
│   │   ├── services/        # Business logic
│   │   ├── utils/           # Utilities
│   │   └── types/           # TypeScript types
│   ├── scripts/             # Database scripts
│   └── Dockerfile
├── database/                 # Database files
│   ├── init.sql            # Initial schema
│   ├── migrations/         # Schema migrations
│   └── seeds/              # Seed data
├── docker-compose.yml       # Main Docker config
├── docker-compose.dev.yml   # Development overrides
├── docker-compose.prod.yml  # Production overrides
├── Makefile                 # Development commands
└── setup.sh                 # Setup script
```

## 🔄 Making Changes

### Frontend Changes
1. Edit files in `frontend/`
2. Changes auto-reload in browser
3. Check console for errors

### Backend Changes
1. Edit files in `backend/src/`
2. Server auto-restarts
3. Check logs: `make logs-backend`

### Database Changes
1. Create migration in `database/migrations/`
2. Run: `make shell-backend && npm run db:migrate`
3. Update seed data if needed

## 📦 Production Deployment

### Build Production Images
```bash
make prod-build
```

### Start Production Services
```bash
make prod-start
```

### Environment Configuration
```bash
# Create production environment file
cp .env.example .env.production

# Edit with production values
# Then start with:
docker-compose -f docker-compose.yml -f docker-compose.prod.yml --env-file .env.production up -d
```

## 🤝 Contributing

1. Create a feature branch
2. Make your changes
3. Test thoroughly:
   ```bash
   make test-frontend
   make test-backend
   make lint-frontend
   make lint-backend
   ```
4. Submit a pull request

## 📚 Additional Resources

- [Next.js Documentation](https://nextjs.org/docs)
- [Express.js Documentation](https://expressjs.com/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Docker Documentation](https://docs.docker.com/)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)

## 🆘 Getting Help

If you encounter issues:

1. Check this troubleshooting guide
2. Review the logs: `make logs`
3. Check service status: `make status`
4. Try a clean restart: `make clean && make setup`
5. Open an issue in the repository

# 10xCFO Deployment Ignore File
# Files and patterns to exclude from S3 deployment

# Development files
*.txt
*.map
*.log
.DS_Store
Thumbs.db

# Unnecessary SVG files from Next.js template
next.svg
vercel.svg
file.svg
globe.svg
window.svg

# Development and build artifacts
node_modules/
.next/
.git/
.gitignore
.env*
*.env

# Documentation and config files
README.md
DEPLOYMENT.md
*.config.js
*.config.ts
*.config.mjs
package.json
pnpm-lock.yaml
yarn.lock
package-lock.json
tsconfig.json
eslint.config.*

# Deployment scripts
deploy-to-s3.sh
setup-aws.sh
.deployignore

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Source maps (optional - uncomment if you don't want them in production)
# *.map

# Test files
*.test.*
*.spec.*
__tests__/
coverage/

# Storybook
.storybook/
storybook-static/

# Cypress
cypress/

# Playwright
playwright-report/
test-results/

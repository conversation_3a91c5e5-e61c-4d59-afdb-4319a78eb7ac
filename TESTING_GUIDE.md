# 10xCFO Limited Functionality - Testing Guide

## Quick Start

### Running the Application
```bash
npm install
npm run dev
```
Application will be available at: `http://localhost:3001`

## Test User Credentials

Use these credentials to test different roles:

| Role | Email | Password |
|------|-------|----------|
| **Admin** | `<EMAIL>` | `password123` |
| **SME** | `<EMAIL>` | `password123` |
| **Consultant** | `<EMAIL>` | `password123` |
| **Investor** | `<EMAIL>` | `password123` |

**Note:** See `TEST_CREDENTIALS.md` for more details and alternative email patterns.

## Testing Checklist by Module

### 1. Admin Module Testing

#### Login & Navigation
- [ ] Login with `<EMAIL>` / `password123`
- [ ] Verify redirect to `/admin/dashboard`
- [ ] Check all navigation links work

#### Dashboard
- [ ] Verify statistics display (Total Users, Pending Requests, Active Reports, Investor Access)
- [ ] Check notification list shows SME report requests
- [ ] Verify quick action cards link to correct pages
- [ ] Test filter buttons (all, pending, in_progress, completed)
- [ ] Test search functionality in notifications

#### User Management (`/admin/dashboard/users`)
- [ ] View all users in the list
- [ ] Test search functionality
- [ ] Test role filter (all, sme, investor, consultant, admin)
- [ ] Test status filter (all, active, inactive, pending)
- [ ] Click "Add New User" button and verify modal opens
- [ ] Test form validation in add user modal
- [ ] Verify permissions button shows for each user
- [ ] Test edit and delete buttons

#### Report Management (`/admin/dashboard/reports`)
- [ ] View all report requests
- [ ] Check stats cards (Pending, Processing, Completed, Shared)
- [ ] Test status filter buttons
- [ ] Test search functionality
- [ ] Click "Execute Report" on pending request
- [ ] Verify external platform integration modal opens
- [ ] Test "Share with Investors" functionality
- [ ] Verify investor selection checkboxes work

#### Access Control (`/admin/dashboard/access-control`)
- [ ] View all investors with access levels
- [ ] Test toggling report access for each investor
- [ ] Test toggling data access for each investor
- [ ] Verify checkmarks update correctly
- [ ] Test "Save Changes" button

### 2. SME Module Testing

#### Login & Navigation
- [ ] Login with `<EMAIL>` / `password123`
- [ ] Verify redirect to `/sme/dashboard`
- [ ] Confirm 10x-growth dashboard is removed (no link/route)

#### Dashboard
- [ ] Verify 3 main action cards display (Documents, Request Report, Schedule Call)
- [ ] Check action items list shows essential tasks only
- [ ] Verify recent activity shows relevant updates
- [ ] Test quick action buttons at bottom
- [ ] Confirm no metrics/analytics are displayed

#### Reports (`/sme/dashboard/reports`)
- [ ] Verify "Request New Report" section displays
- [ ] Test "Request Financial Health Report" button
- [ ] Test "Request Growth Analysis" button
- [ ] Check "Your Report Requests" section shows status
- [ ] Verify status badges (Pending, Processing)
- [ ] View "Reports Shared by Admin" section
- [ ] Test "View" button on shared reports
- [ ] Test "Download" button on shared reports
- [ ] Verify empty state message if no reports

#### Document Upload (`/sme/dashboard/upload`)
- [ ] Navigate to upload page
- [ ] Verify upload interface works
- [ ] Test file selection
- [ ] Check upload progress indicators

#### Schedule Call (`/sme/dashboard/advisor-call`)
- [ ] Navigate to advisor call page
- [ ] Verify scheduling integration component displays
- [ ] Test Calendly link input
- [ ] Test TidyCal link input
- [ ] Test "Save Scheduling Links" button
- [ ] Verify success message appears

### 3. Consultant Module Testing

#### Login & Navigation
- [ ] Login with `<EMAIL>` / `password123`
- [ ] Verify redirect to `/consultant/dashboard`
- [ ] Confirm analytics dashboard is removed (no link/route)

#### Dashboard
- [ ] Verify dashboard displays without analytics
- [ ] Check client management link works
- [ ] Test navigation to onboarding

#### Client Management (`/consultant/dashboard/clients`)
- [ ] View client list
- [ ] Test search functionality
- [ ] Test status filters (all, active, pending, completed)
- [ ] Verify client cards display correctly
- [ ] Test "View Details" button
- [ ] Check progress bars display

#### Client Onboarding (`/consultant/dashboard/onboarding`)
- [ ] Navigate to onboarding page
- [ ] Verify 3-step progress indicator
- [ ] **Step 1 - Company Info**:
  - [ ] Test company name input
  - [ ] Test industry dropdown
  - [ ] Test location input
  - [ ] Click "Next" button
- [ ] **Step 2 - Contact Details**:
  - [ ] Test contact person input
  - [ ] Test email input
  - [ ] Test phone input
  - [ ] Click "Next" button
- [ ] **Step 3 - Business Details**:
  - [ ] Test revenue input
  - [ ] Test employees dropdown
  - [ ] Test description textarea
  - [ ] Click "Complete Onboarding"

#### Post-Onboarding Workflow (`/consultant/dashboard/onboarding/workflow`)
- [ ] Verify success message displays
- [ ] Check "Hand Off to SME" card
- [ ] Check "Request Document Upload" card
- [ ] Test "Hand Off to SME" button
- [ ] Test "Request Documents" button
- [ ] Verify "Go to Clients" link works

#### Scheduling Integration
- [ ] Navigate to schedule page
- [ ] Test Calendly link input
- [ ] Test TidyCal link input
- [ ] Verify save functionality

### 4. Investor Module Testing

#### Login & Navigation
- [ ] Login with `<EMAIL>` / `password123`
- [ ] Verify redirect to `/investor/dashboard`
- [ ] Confirm deal pipeline is removed
- [ ] Confirm search functionality is removed

#### Dashboard
- [ ] Verify welcome message shows view-only context
- [ ] Check access summary cards (Shared Reports, Data Access)
- [ ] Confirm portfolio metrics are removed
- [ ] Verify no self-service features available

#### Shared Reports
- [ ] View "Reports Shared by Admin" section
- [ ] Check report cards display correctly
- [ ] Verify company name, industry, and description show
- [ ] Test "View Report" button
- [ ] Check "Shared by Admin" timestamp
- [ ] Verify empty state if no reports

#### Data Access
- [ ] View "Data Access" section
- [ ] Check data access cards display
- [ ] Verify access level badges (Full Access, Limited Access)
- [ ] Test "View" button on data items
- [ ] Check last updated timestamps

#### Recent Activity
- [ ] Verify activity feed shows admin actions only
- [ ] Check timestamps display correctly
- [ ] Confirm no investor-initiated actions appear

### 5. Cross-Module Testing

#### Authentication
- [ ] Test logout from each role
- [ ] Verify redirect to home page after logout
- [ ] Test login with different roles sequentially
- [ ] Verify role-based routing works correctly
- [ ] Test "Remember me" checkbox

#### Navigation
- [ ] Test home page displays all 4 role cards (SME, Investor, Consultant, Admin)
- [ ] Verify each role card links to correct landing page
- [ ] Test back buttons on all pages
- [ ] Verify breadcrumb navigation where applicable

#### UI/UX
- [ ] Check responsive design on mobile (resize browser)
- [ ] Verify all buttons have hover states
- [ ] Test all modals open and close correctly
- [ ] Check loading states where applicable
- [ ] Verify error messages display appropriately

#### Removed Features Verification
- [ ] Confirm `/sme/dashboard/10x-growth` returns 404 or redirects
- [ ] Confirm `/consultant/dashboard/analytics` returns 404 or redirects
- [ ] Verify no analytics/metrics in simplified dashboards
- [ ] Confirm investors cannot generate reports
- [ ] Verify SMEs cannot self-generate reports

## Common Issues & Solutions

### Issue: Login not working
**Solution**: The app uses mock authentication. Any password works. Make sure email contains the role keyword (admin, sme, consultant, investor).

### Issue: Page not found
**Solution**: Ensure you're using the correct routes. Some old routes have been removed (10x-growth, analytics).

### Issue: Data not persisting
**Solution**: This is expected. The app uses mock data. Refresh will reset all data.

### Issue: Scheduling links not working
**Solution**: Links are stored in state only. Backend integration needed for persistence.

## Performance Testing

- [ ] Test page load times (should be < 2 seconds)
- [ ] Verify smooth transitions between pages
- [ ] Check for console errors (open browser DevTools)
- [ ] Test with multiple tabs open
- [ ] Verify memory usage is reasonable

## Browser Compatibility

Test on:
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

## Accessibility Testing

- [ ] Test keyboard navigation (Tab key)
- [ ] Verify focus indicators are visible
- [ ] Check color contrast ratios
- [ ] Test with screen reader (if available)

## Security Testing

- [ ] Verify role-based access control works
- [ ] Test unauthorized route access
- [ ] Check that admin routes require admin role
- [ ] Verify data isolation between roles

## Bug Reporting Template

When reporting bugs, include:
1. **Role**: Which user role were you testing?
2. **Page**: What page were you on?
3. **Steps**: What did you do?
4. **Expected**: What should have happened?
5. **Actual**: What actually happened?
6. **Screenshot**: If applicable

## Sign-Off Checklist

Before marking testing complete:
- [ ] All critical paths tested
- [ ] All removed features verified as removed
- [ ] All new features tested
- [ ] Cross-browser testing completed
- [ ] Responsive design verified
- [ ] No critical bugs found
- [ ] Documentation reviewed

---

**Testing Version**: 1.0.0
**Last Updated**: January 2025
**Status**: Ready for QA Team

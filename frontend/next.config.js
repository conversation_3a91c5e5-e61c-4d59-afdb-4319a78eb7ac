/** @type {import('next').NextConfig} */
const nextConfig = {
  // Remove static export for Docker development
  // output: 'export', // Commented out for Docker development

  // Enable standalone output for Docker production
  output: process.env.NODE_ENV === 'production' ? 'standalone' : undefined,

  trailingSlash: true,
  skipTrailingSlashRedirect: true,

  images: {
    unoptimized: true,
  },

  reactStrictMode: true,

  // Remove CloudFront asset prefix for Docker setup
  // assetPrefix: process.env.NODE_ENV === 'production' ? 'https://d2tcjdwexogrvt.cloudfront.net' : '',

  eslint: {
    ignoreDuringBuilds: true,
  },

  typescript: {
    ignoreBuildErrors: true,
  },

  // Configure for Docker development
  experimental: {
    // Enable hot reloading in Docker
    turbo: {
      root: __dirname,
    },
  },

  // API rewrites for backend integration
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'}/api/:path*`,
      },
    ];
  },

  // Environment variables
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000',
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  },
};

module.exports = nextConfig;

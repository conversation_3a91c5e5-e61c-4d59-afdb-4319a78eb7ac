# Test Credentials for 10xCFO Platform

## Login Information

Use these credentials to test different user roles in the application.

### Test Accounts

| Role | Email | Password | Dashboard Route |
|------|-------|----------|----------------|
| **Admin** | `<EMAIL>` | `password123` | `/admin/dashboard` |
| **SME** | `<EMAIL>` | `password123` | `/sme/dashboard` |
| **Consultant** | `<EMAIL>` | `password123` | `/consultant/dashboard` |
| **Investor** | `<EMAIL>` | `password123` | `/investor/dashboard` |

## How Authentication Works

The application uses **email pattern matching** for role detection:
- If email contains `admin` → Admin role
- If email contains `sme` → SME role  
- If email contains `consultant` → Consultant role
- If email contains `investor` → Investor role

**Note:** The password can be anything in development mode, but use `password123` for consistency.

## Quick Test Flow

### 1. Admin Testing
```
Email: <EMAIL>
Password: password123
→ Redirects to: /admin/dashboard
```
**Features to test:**
- User management
- Report execution
- Access control
- Notifications

### 2. SME Testing
```
Email: <EMAIL>
Password: password123
→ Redirects to: /sme/dashboard
```
**Features to test:**
- Document upload
- Request reports from admin
- View shared reports
- Schedule calls

### 3. Consultant Testing
```
Email: <EMAIL>
Password: password123
→ Redirects to: /consultant/dashboard
```
**Features to test:**
- Client onboarding (3-step form)
- Post-onboarding workflow
- Client management
- Schedule calls

### 4. Investor Testing
```
Email: <EMAIL>
Password: password123
→ Redirects to: /investor/dashboard
```
**Features to test:**
- View admin-shared reports
- View data access
- View-only interface

## Alternative Email Patterns

You can also use these email patterns (any password works):

**Admin:**
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`

**SME:**
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`

**Consultant:**
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`

**Investor:**
- `<EMAIL>`
- `<EMAIL>`
- `<EMAIL>`

## Important Notes

1. **Mock Authentication**: The app currently uses mock authentication. Any password will work, but we recommend using `password123` for consistency.

2. **Session Persistence**: Login sessions are persisted in browser localStorage. Clear browser data to reset.

3. **Auto-Login**: The app may auto-login with a default investor account on first load. Just logout and login with your desired test account.

4. **Role Switching**: To test different roles, logout and login with a different email pattern.

## Logout

To logout from any role:
1. Click on the user menu/profile icon in the header
2. Click "Logout" button
3. You'll be redirected to the home page

## Troubleshooting

**Issue: Not redirecting to correct dashboard**
- Solution: Make sure the email contains the role keyword (admin, sme, consultant, investor)

**Issue: Already logged in as different role**
- Solution: Logout first, then login with the desired role's email

**Issue: Changes not persisting**
- Solution: This is expected. The app uses mock data. Refresh will reset all data.

---

**Last Updated**: January 2025
**Version**: 1.0.0

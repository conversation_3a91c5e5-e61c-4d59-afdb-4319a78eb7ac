export interface Country {
  code: string
  name: string
  phoneCode: string
}

export interface State {
  code: string
  name: string
  countryCode: string
}

export interface City {
  name: string
  stateCode: string
  countryCode: string
}

export const countries: Country[] = [
  { code: "IN", name: "India", phoneCode: "+91" },
  { code: "AE", name: "UAE", phoneCode: "+971" },
  { code: "US", name: "United States", phoneCode: "+1" },
  { code: "GB", name: "United Kingdom", phoneCode: "+44" },
  { code: "CA", name: "Canada", phoneCode: "+1" },
  { code: "AU", name: "Australia", phoneCode: "+61" },
  { code: "SG", name: "Singapore", phoneCode: "+65" },
  { code: "DE", name: "Germany", phoneCode: "+49" },
  { code: "FR", name: "France", phoneCode: "+33" },
  { code: "JP", name: "Japan", phoneCode: "+81" },
]

export const states: State[] = [
  // India
  { code: "AP", name: "Andhra Pradesh", countryCode: "IN" },
  { code: "AR", name: "Arunachal Pradesh", countryCode: "IN" },
  { code: "AS", name: "Assam", countryCode: "IN" },
  { code: "BR", name: "Bihar", countryCode: "IN" },
  { code: "CT", name: "Chhattisgarh", countryCode: "IN" },
  { code: "GA", name: "Goa", countryCode: "IN" },
  { code: "GJ", name: "Gujarat", countryCode: "IN" },
  { code: "HR", name: "Haryana", countryCode: "IN" },
  { code: "HP", name: "Himachal Pradesh", countryCode: "IN" },
  { code: "JH", name: "Jharkhand", countryCode: "IN" },
  { code: "KA", name: "Karnataka", countryCode: "IN" },
  { code: "KL", name: "Kerala", countryCode: "IN" },
  { code: "MP", name: "Madhya Pradesh", countryCode: "IN" },
  { code: "MH", name: "Maharashtra", countryCode: "IN" },
  { code: "MN", name: "Manipur", countryCode: "IN" },
  { code: "ML", name: "Meghalaya", countryCode: "IN" },
  { code: "MZ", name: "Mizoram", countryCode: "IN" },
  { code: "NL", name: "Nagaland", countryCode: "IN" },
  { code: "OR", name: "Odisha", countryCode: "IN" },
  { code: "PB", name: "Punjab", countryCode: "IN" },
  { code: "RJ", name: "Rajasthan", countryCode: "IN" },
  { code: "SK", name: "Sikkim", countryCode: "IN" },
  { code: "TN", name: "Tamil Nadu", countryCode: "IN" },
  { code: "TG", name: "Telangana", countryCode: "IN" },
  { code: "TR", name: "Tripura", countryCode: "IN" },
  { code: "UP", name: "Uttar Pradesh", countryCode: "IN" },
  { code: "UT", name: "Uttarakhand", countryCode: "IN" },
  { code: "WB", name: "West Bengal", countryCode: "IN" },
  { code: "DL", name: "Delhi", countryCode: "IN" },
  
  // UAE
  { code: "AZ", name: "Abu Dhabi", countryCode: "AE" },
  { code: "AJ", name: "Ajman", countryCode: "AE" },
  { code: "DU", name: "Dubai", countryCode: "AE" },
  { code: "FU", name: "Fujairah", countryCode: "AE" },
  { code: "RA", name: "Ras Al Khaimah", countryCode: "AE" },
  { code: "SH", name: "Sharjah", countryCode: "AE" },
  { code: "UQ", name: "Umm Al Quwain", countryCode: "AE" },
  
  // US
  { code: "AL", name: "Alabama", countryCode: "US" },
  { code: "AK", name: "Alaska", countryCode: "US" },
  { code: "AZ", name: "Arizona", countryCode: "US" },
  { code: "AR", name: "Arkansas", countryCode: "US" },
  { code: "CA", name: "California", countryCode: "US" },
  { code: "CO", name: "Colorado", countryCode: "US" },
  { code: "CT", name: "Connecticut", countryCode: "US" },
  { code: "DE", name: "Delaware", countryCode: "US" },
  { code: "FL", name: "Florida", countryCode: "US" },
  { code: "GA", name: "Georgia", countryCode: "US" },
  { code: "HI", name: "Hawaii", countryCode: "US" },
  { code: "ID", name: "Idaho", countryCode: "US" },
  { code: "IL", name: "Illinois", countryCode: "US" },
  { code: "IN", name: "Indiana", countryCode: "US" },
  { code: "IA", name: "Iowa", countryCode: "US" },
  { code: "KS", name: "Kansas", countryCode: "US" },
  { code: "KY", name: "Kentucky", countryCode: "US" },
  { code: "LA", name: "Louisiana", countryCode: "US" },
  { code: "ME", name: "Maine", countryCode: "US" },
  { code: "MD", name: "Maryland", countryCode: "US" },
  { code: "MA", name: "Massachusetts", countryCode: "US" },
  { code: "MI", name: "Michigan", countryCode: "US" },
  { code: "MN", name: "Minnesota", countryCode: "US" },
  { code: "MS", name: "Mississippi", countryCode: "US" },
  { code: "MO", name: "Missouri", countryCode: "US" },
  { code: "MT", name: "Montana", countryCode: "US" },
  { code: "NE", name: "Nebraska", countryCode: "US" },
  { code: "NV", name: "Nevada", countryCode: "US" },
  { code: "NH", name: "New Hampshire", countryCode: "US" },
  { code: "NJ", name: "New Jersey", countryCode: "US" },
  { code: "NM", name: "New Mexico", countryCode: "US" },
  { code: "NY", name: "New York", countryCode: "US" },
  { code: "NC", name: "North Carolina", countryCode: "US" },
  { code: "ND", name: "North Dakota", countryCode: "US" },
  { code: "OH", name: "Ohio", countryCode: "US" },
  { code: "OK", name: "Oklahoma", countryCode: "US" },
  { code: "OR", name: "Oregon", countryCode: "US" },
  { code: "PA", name: "Pennsylvania", countryCode: "US" },
  { code: "RI", name: "Rhode Island", countryCode: "US" },
  { code: "SC", name: "South Carolina", countryCode: "US" },
  { code: "SD", name: "South Dakota", countryCode: "US" },
  { code: "TN", name: "Tennessee", countryCode: "US" },
  { code: "TX", name: "Texas", countryCode: "US" },
  { code: "UT", name: "Utah", countryCode: "US" },
  { code: "VT", name: "Vermont", countryCode: "US" },
  { code: "VA", name: "Virginia", countryCode: "US" },
  { code: "WA", name: "Washington", countryCode: "US" },
  { code: "WV", name: "West Virginia", countryCode: "US" },
  { code: "WI", name: "Wisconsin", countryCode: "US" },
  { code: "WY", name: "Wyoming", countryCode: "US" },
]

export const cities: City[] = [
  // India
  { name: "Mumbai", stateCode: "MH", countryCode: "IN" },
  { name: "Delhi", stateCode: "DL", countryCode: "IN" },
  { name: "Bangalore", stateCode: "KA", countryCode: "IN" },
  { name: "Hyderabad", stateCode: "TG", countryCode: "IN" },
  { name: "Chennai", stateCode: "TN", countryCode: "IN" },
  { name: "Kolkata", stateCode: "WB", countryCode: "IN" },
  { name: "Pune", stateCode: "MH", countryCode: "IN" },
  { name: "Ahmedabad", stateCode: "GJ", countryCode: "IN" },
  { name: "Jaipur", stateCode: "RJ", countryCode: "IN" },
  { name: "Surat", stateCode: "GJ", countryCode: "IN" },
  { name: "Lucknow", stateCode: "UP", countryCode: "IN" },
  { name: "Kanpur", stateCode: "UP", countryCode: "IN" },
  { name: "Nagpur", stateCode: "MH", countryCode: "IN" },
  { name: "Indore", stateCode: "MP", countryCode: "IN" },
  { name: "Thane", stateCode: "MH", countryCode: "IN" },
  { name: "Bhopal", stateCode: "MP", countryCode: "IN" },
  { name: "Visakhapatnam", stateCode: "AP", countryCode: "IN" },
  { name: "Pimpri-Chinchwad", stateCode: "MH", countryCode: "IN" },
  { name: "Patna", stateCode: "BR", countryCode: "IN" },
  { name: "Vadodara", stateCode: "GJ", countryCode: "IN" },
  { name: "Ghaziabad", stateCode: "UP", countryCode: "IN" },
  { name: "Ludhiana", stateCode: "PB", countryCode: "IN" },
  { name: "Agra", stateCode: "UP", countryCode: "IN" },
  { name: "Nashik", stateCode: "MH", countryCode: "IN" },
  { name: "Faridabad", stateCode: "HR", countryCode: "IN" },
  { name: "Meerut", stateCode: "UP", countryCode: "IN" },
  { name: "Rajkot", stateCode: "GJ", countryCode: "IN" },
  { name: "Kalyan-Dombivali", stateCode: "MH", countryCode: "IN" },
  { name: "Vasai-Virar", stateCode: "MH", countryCode: "IN" },
  { name: "Varanasi", stateCode: "UP", countryCode: "IN" },

  // UAE
  { name: "Dubai", stateCode: "DU", countryCode: "AE" },
  { name: "Abu Dhabi", stateCode: "AZ", countryCode: "AE" },
  { name: "Sharjah", stateCode: "SH", countryCode: "AE" },
  { name: "Al Ain", stateCode: "AZ", countryCode: "AE" },
  { name: "Ajman", stateCode: "AJ", countryCode: "AE" },
  { name: "Ras Al Khaimah", stateCode: "RA", countryCode: "AE" },
  { name: "Fujairah", stateCode: "FU", countryCode: "AE" },
  { name: "Umm Al Quwain", stateCode: "UQ", countryCode: "AE" },

  // US
  { name: "New York", stateCode: "NY", countryCode: "US" },
  { name: "Los Angeles", stateCode: "CA", countryCode: "US" },
  { name: "Chicago", stateCode: "IL", countryCode: "US" },
  { name: "Houston", stateCode: "TX", countryCode: "US" },
  { name: "Phoenix", stateCode: "AZ", countryCode: "US" },
  { name: "Philadelphia", stateCode: "PA", countryCode: "US" },
  { name: "San Antonio", stateCode: "TX", countryCode: "US" },
  { name: "San Diego", stateCode: "CA", countryCode: "US" },
  { name: "Dallas", stateCode: "TX", countryCode: "US" },
  { name: "San Jose", stateCode: "CA", countryCode: "US" },
  { name: "Austin", stateCode: "TX", countryCode: "US" },
  { name: "Jacksonville", stateCode: "FL", countryCode: "US" },
  { name: "Fort Worth", stateCode: "TX", countryCode: "US" },
  { name: "Columbus", stateCode: "OH", countryCode: "US" },
  { name: "Charlotte", stateCode: "NC", countryCode: "US" },
  { name: "San Francisco", stateCode: "CA", countryCode: "US" },
  { name: "Indianapolis", stateCode: "IN", countryCode: "US" },
  { name: "Seattle", stateCode: "WA", countryCode: "US" },
  { name: "Denver", stateCode: "CO", countryCode: "US" },
  { name: "Washington", stateCode: "DC", countryCode: "US" },
]

export const getStatesByCountry = (countryCode: string): State[] => {
  return states.filter(state => state.countryCode === countryCode)
}

export const getCitiesByState = (stateCode: string, countryCode: string): City[] => {
  return cities.filter(city => city.stateCode === stateCode && city.countryCode === countryCode)
}

export const getCitiesByCountry = (countryCode: string): City[] => {
  return cities.filter(city => city.countryCode === countryCode)
}

export const getCountryByCode = (code: string): Country | undefined => {
  return countries.find(country => country.code === code)
}

export const getPhoneCodeByCountry = (countryCode: string): string => {
  const country = getCountryByCode(countryCode)
  return country?.phoneCode || "+1"
}

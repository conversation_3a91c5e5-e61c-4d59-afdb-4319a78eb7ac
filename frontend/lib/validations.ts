import { z } from 'zod';

// Auth validation schemas
export const signInSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
});

export const signUpSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
  confirmPassword: z.string(),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  role: z.enum(['sme', 'investor', 'consultant'], {
    required_error: 'Please select a role',
  }),
  companyName: z.string().min(2, 'Company name must be at least 2 characters').optional(),
  agreeToTerms: z.boolean().refine((val) => val === true, {
    message: 'You must agree to the terms and conditions',
  }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword'],
});

export const otpVerificationSchema = z.object({
  otp: z.string().length(6, 'OTP must be exactly 6 digits').regex(/^\d+$/, 'OTP must contain only numbers'),
});

// Profile validation schemas
export const profileSetupSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  companyName: z.string().min(2, 'Company name must be at least 2 characters').optional(),
  industry: z.string().min(2, 'Please select an industry').optional(),
  companySize: z.enum(['1-10', '11-50', '51-200', '201-1000', '1000+'], {
    required_error: 'Please select company size',
  }).optional(),
  annualRevenue: z.enum(['<1L', '1L-10L', '10L-1Cr', '1Cr-10Cr', '10Cr+'], {
    required_error: 'Please select annual revenue range',
  }).optional(),
  phone: z.string().regex(/^\+?[\d\s-()]+$/, 'Please enter a valid phone number').optional(),
  website: z.string().url('Please enter a valid website URL').optional(),
});

// Financial data validation schemas
export const financialDataSchema = z.object({
  revenue: z.number().min(0, 'Revenue must be a positive number'),
  profit: z.number(),
  expenses: z.number().min(0, 'Expenses must be a positive number'),
  assets: z.number().min(0, 'Assets must be a positive number'),
  liabilities: z.number().min(0, 'Liabilities must be a positive number'),
  cashFlow: z.number(),
  period: z.enum(['monthly', 'quarterly', 'yearly']),
  year: z.number().min(2020).max(new Date().getFullYear()),
});

// File upload validation
export const fileUploadSchema = z.object({
  files: z.array(z.instanceof(File)).min(1, 'Please select at least one file'),
  category: z.enum(['financial_statements', 'tax_returns', 'bank_statements', 'other'], {
    required_error: 'Please select a document category',
  }),
});

// Contact form validation
export const contactFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  company: z.string().min(2, 'Company name must be at least 2 characters').optional(),
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  message: z.string().min(10, 'Message must be at least 10 characters'),
});

// Investment preferences validation (for investors)
export const investmentPreferencesSchema = z.object({
  minInvestment: z.number().min(100000, 'Minimum investment must be at least ₹1 Lakh'),
  maxInvestment: z.number().min(100000, 'Maximum investment must be at least ₹1 Lakh'),
  preferredIndustries: z.array(z.string()).min(1, 'Please select at least one industry'),
  riskTolerance: z.enum(['low', 'medium', 'high'], {
    required_error: 'Please select risk tolerance',
  }),
  investmentHorizon: z.enum(['1-2 years', '3-5 years', '5+ years'], {
    required_error: 'Please select investment horizon',
  }),
  preferredStage: z.enum(['seed', 'series-a', 'series-b', 'growth', 'any'], {
    required_error: 'Please select preferred investment stage',
  }),
});

// Type exports for TypeScript
export type SignInFormData = z.infer<typeof signInSchema>;
export type SignUpFormData = z.infer<typeof signUpSchema>;
export type OTPVerificationData = z.infer<typeof otpVerificationSchema>;
export type ProfileSetupData = z.infer<typeof profileSetupSchema>;
export type FinancialData = z.infer<typeof financialDataSchema>;
export type FileUploadData = z.infer<typeof fileUploadSchema>;
export type ContactFormData = z.infer<typeof contactFormSchema>;
export type InvestmentPreferencesData = z.infer<typeof investmentPreferencesSchema>;

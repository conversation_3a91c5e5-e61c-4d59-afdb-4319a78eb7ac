"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Calendar, ExternalLink, Save } from "lucide-react";
import { useState } from "react";

interface SchedulingIntegrationProps {
  userType: "sme" | "consultant";
}

export default function SchedulingIntegration({ userType }: SchedulingIntegrationProps) {
  const [calendlyLink, setCalendlyLink] = useState("");
  const [tidycalLink, setTidycalLink] = useState("");
  const [saved, setSaved] = useState(false);

  const handleSave = () => {
    // Save scheduling links to user profile
    console.log("Saving scheduling links:", { calendlyLink, tidycalLink });
    setSaved(true);
    setTimeout(() => setSaved(false), 3000);
  };

  return (
    <Card className="bg-white border-gray-200 shadow-sm">
      <CardHeader>
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <Calendar className="w-6 h-6 text-blue-600" />
          </div>
          <div>
            <CardTitle className="text-black">Scheduling Integration</CardTitle>
            <CardDescription className="text-gray-600">
              Connect your Calendly or TidyCal account for easy scheduling
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Calendly Integration */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="calendly" className="text-gray-700 font-medium">
              Calendly Link
            </Label>
            <a
              href="https://calendly.com"
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm text-blue-600 hover:text-blue-700 flex items-center"
            >
              Get Calendly
              <ExternalLink className="w-3 h-3 ml-1" />
            </a>
          </div>
          <Input
            id="calendly"
            type="url"
            placeholder="https://calendly.com/your-username"
            value={calendlyLink}
            onChange={(e) => setCalendlyLink(e.target.value)}
            className="bg-white border-gray-300"
          />
          <p className="text-xs text-gray-500">
            Paste your Calendly scheduling link here
          </p>
        </div>

        {/* TidyCal Integration */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label htmlFor="tidycal" className="text-gray-700 font-medium">
              TidyCal Link
            </Label>
            <a
              href="https://tidycal.com"
              target="_blank"
              rel="noopener noreferrer"
              className="text-sm text-blue-600 hover:text-blue-700 flex items-center"
            >
              Get TidyCal
              <ExternalLink className="w-3 h-3 ml-1" />
            </a>
          </div>
          <Input
            id="tidycal"
            type="url"
            placeholder="https://tidycal.com/your-username"
            value={tidycalLink}
            onChange={(e) => setTidycalLink(e.target.value)}
            className="bg-white border-gray-300"
          />
          <p className="text-xs text-gray-500">
            Paste your TidyCal scheduling link here
          </p>
        </div>

        {/* Save Button */}
        <div className="pt-4 border-t border-gray-200">
          <Button
            onClick={handleSave}
            className="w-full !bg-black !text-white"
            disabled={!calendlyLink && !tidycalLink}
          >
            <Save className="w-4 h-4 mr-2" />
            {saved ? "Saved!" : "Save Scheduling Links"}
          </Button>
        </div>

        {/* Info Box */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-semibold text-blue-900 mb-2">How it works</h4>
          <ul className="space-y-1 text-sm text-blue-800">
            <li>• Add your Calendly or TidyCal link to your profile</li>
            <li>• {userType === "sme" ? "Clients and advisors" : "Clients"} can book calls directly</li>
            <li>• Automatic calendar synchronization</li>
            <li>• Reduce back-and-forth scheduling emails</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}

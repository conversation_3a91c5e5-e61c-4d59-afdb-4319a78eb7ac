"use client";

import { Button } from "@/components/ui/button";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface NavigationProps {
  variant?: "default" | "sme" | "investor" | "consultant";
}

export default function MainNavigation({ variant = "default" }: NavigationProps) {
  const pathname = usePathname();

  const isActive = (path: string) => {
    if (path === "/") return pathname === "/";
    return pathname.startsWith(path);
  };

  const getActiveClass = (path: string) => {
    return isActive(path) 
      ? "text-black font-semibold border-b-2 border-black" 
      : "text-gray-600 hover:text-black transition-colors";
  };

  const getButtonConfig = () => {
    switch (variant) {
      case "sme": 
        return { variant: "primary-black" as const, text: "Get Started" };
      case "investor": 
        return { variant: "primary-black" as const, text: "Access Deals" };
      case "consultant": 
        return { variant: "primary-black" as const, text: "Start Consulting" };
      default: 
        return { variant: "primary-white" as const, text: "Sign up" };
    }
  };

  const buttonConfig = getButtonConfig();

  return (
    <nav className="hidden md:flex items-center space-x-8">
      <Link href="/sme" className={getActiveClass("/sme")}>
        For SMEs
      </Link>
      <Link href="/investor" className={getActiveClass("/investor")}>
        For Investors
      </Link>
      <Link href="/consultant" className={getActiveClass("/consultant")}>
        For Consultants
      </Link>
      <Link href="/10x-growth-hack" className={getActiveClass("/10x-growth-hack")}>
        10X Growth
      </Link>
      <Link href="/pricing" className={getActiveClass("/pricing")}>
        Pricing
      </Link>
      <Link href="/auth/signin" className={getActiveClass("/auth/signin")}>
        Log in
      </Link>
      
      {variant === "default" ? (
        <Link href="/auth/signup">
          <Button variant={buttonConfig.variant} className="rounded-full px-6">
            {buttonConfig.text}
          </Button>
        </Link>
      ) : (
        <Button variant="primary-black" className="rounded-full px-6">
          {buttonConfig.text}
        </Button>
      )}
    </nav>
  );
}
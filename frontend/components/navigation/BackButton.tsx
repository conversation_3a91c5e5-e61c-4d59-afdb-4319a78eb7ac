"use client";

import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useAuthStore } from "@/stores/authStore";

interface BackButtonProps {
  href?: string;
  label?: string;
  className?: string;
}

export default function BackButton({ 
  href, 
  label = "Back to Dashboard", 
  className = "" 
}: BackButtonProps) {
  const { user } = useAuthStore();

  // Auto-determine dashboard path if no href provided
  const getDefaultHref = () => {
    if (href) return href;
    
    switch (user?.role) {
      case 'investor': return '/investor/dashboard';
      case 'consultant': return '/consultant/dashboard';
      default: return '/sme/dashboard';
    }
  };

  const backHref = getDefaultHref();

  return (
    <div className={`container mx-auto px-4 py-4 ${className}`}>
      <Link 
        href={backHref} 
        className="flex items-center space-x-3 text-slate-400 hover:text-white transition-colors"
      >
        <ArrowLeft className="w-5 h-5" />
        <span>{label}</span>
      </Link>
    </div>
  );
}

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "./card";
import { Progress } from "./progress";
import { Badge } from "./badge";
import { Button } from "./button";
import { LucideIcon } from "lucide-react";
import React from "react";

interface DashboardCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  progress?: number;
  badge?: string;
  action?: {
    label: string;
    onClick: () => void;
    variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | "primary-black" | "primary-white" | "outline-black" | "outline-gray";
  };
}

export function DashboardCard({
  title,
  value,
  icon,
  progress,
  badge,
  action,
}: DashboardCardProps) {
  return (
    <Card className="bg-white border border-gray-200 shadow-sm text-black">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-gray-700">
          {title}
        </CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-black">{value}</div>
        {progress !== undefined && (
          <div className="mt-4">
            <Progress value={progress} showValue={true} />
          </div>
        )}
        {badge && (
          <Badge variant="default" className="mt-2">
            {badge}
          </Badge>
        )}
        {action && (
          <Button
            variant={action.variant || "ghost"}
            onClick={action.onClick}
            className="mt-4 w-full"
          >
            {action.label}
          </Button>
        )}
      </CardContent>
    </Card>
  );
}

interface QuickActionProps {
  label: string;
  icon: React.ReactNode;
  href: string;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | "primary-black" | "primary-white" | "outline-black" | "outline-gray";
}

export function QuickAction({
  label,
  icon,
  href,
  variant = "primary-black",
}: QuickActionProps) {
  return (
    <Button asChild variant={variant} className="w-full py-6 flex-col h-auto">
      <a href={href} className="flex flex-col items-center justify-center gap-2">
        {icon}
        <span className="text-sm font-medium text-current">{label}</span>
      </a>
    </Button>
  );
}
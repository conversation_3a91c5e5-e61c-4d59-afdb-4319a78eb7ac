"use client"

import * as React from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"

interface SelectWithOtherProps {
  id: string
  label: string
  value: string
  onChange: (value: string) => void
  options: { value: string; label: string }[]
  placeholder?: string
  required?: boolean
  className?: string
  otherPlaceholder?: string
}

export function SelectWithOther({
  id,
  label,
  value,
  onChange,
  options,
  placeholder = "Select an option",
  required = false,
  className = "",
  otherPlaceholder = "Please specify..."
}: SelectWithOtherProps) {
  const [showOtherInput, setShowOtherInput] = React.useState(false)
  const [otherValue, setOtherValue] = React.useState("")

  React.useEffect(() => {
    // Check if current value is not in the predefined options
    const isOtherValue = value && !options.some(option => option.value === value)
    if (isOtherValue) {
      setShowOtherInput(true)
      setOtherValue(value)
    } else {
      setShowOtherInput(false)
      setOtherValue("")
    }
  }, [value, options])

  const handleSelectChange = (selectedValue: string) => {
    if (selectedValue === "other") {
      setShowOtherInput(true)
      setOtherValue("")
      onChange("")
    } else {
      setShowOtherInput(false)
      setOtherValue("")
      onChange(selectedValue)
    }
  }

  const handleOtherInputChange = (inputValue: string) => {
    setOtherValue(inputValue)
    onChange(inputValue)
  }

  return (
    <div className="space-y-2">
      <Label htmlFor={id} className="text-gray-700">
        {label} {required && "*"}
      </Label>
      <select
        id={id}
        value={showOtherInput ? "other" : value}
        onChange={(e) => handleSelectChange(e.target.value)}
        className={`w-full bg-white border border-gray-300 text-black rounded-md px-3 py-2 ${className}`}
      >
        <option value="">{placeholder}</option>
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
        <option value="other">Other</option>
      </select>
      
      {showOtherInput && (
        <Input
          value={otherValue}
          onChange={(e) => handleOtherInputChange(e.target.value)}
          className="bg-white border-gray-300 text-black"
          placeholder={otherPlaceholder}
        />
      )}
    </div>
  )
}

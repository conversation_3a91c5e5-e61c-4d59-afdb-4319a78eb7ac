"use client"

import * as React from "react"
import * as ProgressPrimitive from "@radix-ui/react-progress"

import { cn } from "@/lib/utils"

interface ProgressProps extends React.ComponentProps<typeof ProgressPrimitive.Root> {
  value?: number
  variant?: 'default' | 'success' | 'warning' | 'error'
  showValue?: boolean
}

function Progress({
  className,
  value = 0,
  variant = 'default',
  showValue = false,
  ...props
}: ProgressProps) {
  const variantStyles = {
    default: "bg-black",
    success: "bg-green-600",
    warning: "bg-amber-500",
    error: "bg-red-600"
  }

  return (
    <div className="w-full">
      <ProgressPrimitive.Root
        data-slot="progress"
        className={cn(
          "relative h-2 w-full overflow-hidden rounded-full bg-gray-200",
          className
        )}
        {...props}
      >
        <ProgressPrimitive.Indicator
          data-slot="progress-indicator"
          className={cn(
            "h-full w-full flex-1 transition-all duration-300 ease-out",
            variantStyles[variant]
          )}
          style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
        />
      </ProgressPrimitive.Root>
      {showValue && (
        <div className="mt-1 text-xs text-gray-600 text-right">
          {Math.round(value || 0)}%
        </div>
      )}
    </div>
  )
}

export { Progress }
"use client";

import AppHeader from "@/components/navigation/AppHeader";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { motion } from "framer-motion";
import { 
  Bell, 
  Users, 
  FileText, 
  Shield, 
  Search,
  CheckCircle,
  Clock,
  AlertCircle,
  ExternalLink
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface Notification {
  id: number;
  type: "report_request" | "user_registration" | "system_alert";
  title: string;
  message: string;
  user: string;
  timestamp: string;
  status: "pending" | "completed" | "in_progress";
}

export default function AdminDashboard() {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");

  // Mock notifications data
  const notifications: Notification[] = [
    {
      id: 1,
      type: "report_request",
      title: "Report Request from TechCorp Solutions",
      message: "SME has requested financial health report generation",
      user: "<PERSON> (TechCorp Solutions)",
      timestamp: "2 hours ago",
      status: "pending"
    },
    {
      id: 2,
      type: "report_request",
      title: "Report Request from GreenEnergy Ltd",
      message: "SME has requested growth analysis report",
      user: "Sarah Johnson (GreenEnergy Ltd)",
      timestamp: "5 hours ago",
      status: "in_progress"
    },
    {
      id: 3,
      type: "user_registration",
      title: "New Investor Registration",
      message: "New investor account pending approval",
      user: "Michael Chen",
      timestamp: "1 day ago",
      status: "pending"
    },
    {
      id: 4,
      type: "report_request",
      title: "Report Request from HealthTech Innovations",
      message: "SME has requested market analysis report",
      user: "Dr. Raj Patel (HealthTech)",
      timestamp: "2 days ago",
      status: "completed"
    }
  ];

  const stats = {
    totalUsers: 156,
    pendingRequests: 8,
    activeReports: 12,
    investorAccess: 24
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "report_request":
        return <FileText className="w-5 h-5 text-blue-600" />;
      case "user_registration":
        return <Users className="w-5 h-5 text-green-600" />;
      case "system_alert":
        return <AlertCircle className="w-5 h-5 text-orange-600" />;
      default:
        return <Bell className="w-5 h-5 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-700 border-yellow-200">Pending</Badge>;
      case "in_progress":
        return <Badge className="bg-blue-100 text-blue-700 border-blue-200">In Progress</Badge>;
      case "completed":
        return <Badge className="bg-green-100 text-green-700 border-green-200">Completed</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-700 border-gray-200">Unknown</Badge>;
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         notification.user.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === "all" || notification.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="default" />

      <div className="container mx-auto px-6 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-2">
            <Shield className="w-8 h-8 text-black" />
            <h1 className="text-3xl font-bold text-black">Admin Dashboard</h1>
          </div>
          <p className="text-gray-600">Manage platform operations and user requests</p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-600 text-sm">Total Users</p>
                    <p className="text-3xl font-bold text-black">{stats.totalUsers}</p>
                  </div>
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Users className="w-6 h-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-600 text-sm">Pending Requests</p>
                    <p className="text-3xl font-bold text-black">{stats.pendingRequests}</p>
                  </div>
                  <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <Clock className="w-6 h-6 text-yellow-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-600 text-sm">Active Reports</p>
                    <p className="text-3xl font-bold text-black">{stats.activeReports}</p>
                  </div>
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <FileText className="w-6 h-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-600 text-sm">Investor Access</p>
                    <p className="text-3xl font-bold text-black">{stats.investorAccess}</p>
                  </div>
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Shield className="w-6 h-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Link href="/admin/dashboard/users">
            <Card className="bg-white border-gray-200 hover:border-gray-400 transition-all cursor-pointer">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Users className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-black">User Management</h3>
                    <p className="text-sm text-gray-600">Manage all platform users</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>

          <Link href="/admin/dashboard/reports">
            <Card className="bg-white border-gray-200 hover:border-gray-400 transition-all cursor-pointer">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <FileText className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-black">Report Execution</h3>
                    <p className="text-sm text-gray-600">Execute and manage reports</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>

          <Link href="/admin/dashboard/access-control">
            <Card className="bg-white border-gray-200 hover:border-gray-400 transition-all cursor-pointer">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Shield className="w-6 h-6 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-black">Access Control</h3>
                    <p className="text-sm text-gray-600">Manage permissions</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
        </div>

        {/* Notifications Section */}
        <Card className="bg-white border-gray-200 shadow-sm">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-black flex items-center space-x-2">
                <Bell className="w-5 h-5" />
                <span>Recent Notifications</span>
              </CardTitle>
              <div className="flex gap-2">
                {["all", "pending", "in_progress", "completed"].map((status) => (
                  <Button
                    key={status}
                    variant={filterStatus === status ? "default" : "outline"}
                    size="sm"
                    onClick={() => setFilterStatus(status)}
                    className={filterStatus === status ? "!bg-black !text-white" : ""}
                  >
                    {status.charAt(0).toUpperCase() + status.slice(1).replace("_", " ")}
                  </Button>
                ))}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-4 h-4" />
                <Input
                  placeholder="Search notifications..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-white border-gray-300 text-black"
                />
              </div>
            </div>

            <div className="space-y-4">
              {filteredNotifications.map((notification, index) => (
                <motion.div
                  key={notification.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  className="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className="mt-1">
                        {getNotificationIcon(notification.type)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-semibold text-black">{notification.title}</h4>
                          {getStatusBadge(notification.status)}
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{notification.message}</p>
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          <span>{notification.user}</span>
                          <span>•</span>
                          <span>{notification.timestamp}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      {notification.type === "report_request" && notification.status === "pending" && (
                        <Link href={`/admin/dashboard/reports/${notification.id}`}>
                          <Button size="sm" className="!bg-black !text-white">
                            Process Request
                          </Button>
                        </Link>
                      )}
                      {notification.status === "in_progress" && (
                        <Button size="sm" variant="outline">
                          View Details
                        </Button>
                      )}
                      {notification.status === "completed" && (
                        <CheckCircle className="w-5 h-5 text-green-600" />
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {filteredNotifications.length === 0 && (
              <div className="text-center py-12">
                <Bell className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-black mb-2">No notifications found</h3>
                <p className="text-gray-600">Try adjusting your search or filter criteria</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

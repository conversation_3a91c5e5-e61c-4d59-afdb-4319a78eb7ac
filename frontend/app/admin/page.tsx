"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Logo from "@/components/ui/logo";
import { motion } from "framer-motion";
import { ArrowRight, Shield, Users, FileText, Settings } from "lucide-react";
import Link from "next/link";

export default function AdminLanding() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center mb-8">
          <Logo href="/" size="lg" />
        </div>

        <div className="max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <Badge className="mb-4 bg-white/20 text-white border-white/30">
              <Shield className="w-4 h-4 mr-2" />
              Admin Portal
            </Badge>
            <h1 className="text-5xl font-bold text-white mb-4">
              Platform Administration
            </h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Manage users, execute reports, and control data access across the 10xCFO platform
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <Card className="bg-white/10 border-white/20 backdrop-blur-sm hover:bg-white/15 transition-all">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                    <Users className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">User Management</h3>
                  <p className="text-gray-300 mb-4">
                    Manage user accounts, roles, and permissions across the platform
                  </p>
                  <ul className="space-y-2 text-sm text-gray-300">
                    <li>• Create and manage user accounts</li>
                    <li>• Assign roles and permissions</li>
                    <li>• Monitor user activity</li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="bg-white/10 border-white/20 backdrop-blur-sm hover:bg-white/15 transition-all">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                    <FileText className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">Report Management</h3>
                  <p className="text-gray-300 mb-4">
                    Execute reports and control data sharing with investors
                  </p>
                  <ul className="space-y-2 text-sm text-gray-300">
                    <li>• Process SME report requests</li>
                    <li>• Execute reports via external platform</li>
                    <li>• Share data with investors</li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Card className="bg-white/10 border-white/20 backdrop-blur-sm hover:bg-white/15 transition-all">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                    <Shield className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">Access Control</h3>
                  <p className="text-gray-300 mb-4">
                    Granular permission system for data and report visibility
                  </p>
                  <ul className="space-y-2 text-sm text-gray-300">
                    <li>• Control investor data access</li>
                    <li>• Manage report visibility</li>
                    <li>• Set granular permissions</li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <Card className="bg-white/10 border-white/20 backdrop-blur-sm hover:bg-white/15 transition-all">
                <CardContent className="p-6">
                  <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center mb-4">
                    <Settings className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">Notifications</h3>
                  <p className="text-gray-300 mb-4">
                    Receive and manage SME report requests and system alerts
                  </p>
                  <ul className="space-y-2 text-sm text-gray-300">
                    <li>• SME report request alerts</li>
                    <li>• System notifications</li>
                    <li>• Activity monitoring</li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
            className="text-center"
          >
            <Link href="/admin/dashboard">
              <Button size="lg" className="!bg-white !text-slate-900 hover:!bg-gray-100 px-8 py-4 text-lg font-medium">
                Access Admin Dashboard
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <p className="mt-4 text-gray-400 text-sm">
              Secure admin access required
            </p>
          </motion.div>
        </div>
      </div>
    </div>
  );
}

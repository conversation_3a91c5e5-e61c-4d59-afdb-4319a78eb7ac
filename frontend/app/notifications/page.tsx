"use client";

import AppHeader from "@/components/navigation/AppHeader";
import BackButton from "@/components/navigation/BackButton";
import { useAuthStore } from "@/stores/authStore";
import { motion } from "framer-motion";
import { useState } from "react";

export default function NotificationsPage() {
  const { user } = useAuthStore();
  const [filter, setFilter] = useState("all");

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <AppHeader />

      {/* Back Navigation */}
      <BackButton />

      <div className="container mx-auto px-4 py-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center"
        >
          <h1 className="text-3xl font-bold text-black mb-4">Notifications</h1>
          <p className="text-gray-600">Stay updated with your latest activities and alerts</p>
        </motion.div>
      </div>
    </div>
  );
}
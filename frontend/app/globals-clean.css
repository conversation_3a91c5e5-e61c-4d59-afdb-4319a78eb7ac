@import "tailwindcss";
@import "tw-animate-css";

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans), ui-sans-serif, -apple-system, 'Segoe UI', 'Helvetica Neue', 'Noto Sans', 'Noto Sans CJK JP', 'Noto Sans CJK KR', 'Noto Sans CJK SC', 'Noto Sans CJK TC', sans-serif;
  --font-mono: var(--font-geist-mono), ui-monospace, 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;

  /* Grayscale Colors for Light Theme */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;
  /* Light Theme Variables - Black and White Design */
  --background: #ffffff;
  --foreground: #000000;
  --card: #ffffff;
  --card-foreground: #000000;
  --popover: #ffffff;
  --popover-foreground: #000000;
  --primary: #000000; /* Black */
  --primary-foreground: #ffffff; /* White */
  --secondary: #f3f4f6; /* Light Gray */
  --secondary-foreground: #000000; /* Black */
  --muted: #e5e7eb; /* Lighter Gray */
  --muted-foreground: #6b7280; /* Medium Gray */
  --accent: #f9fafb; /* Very Light Gray */
  --accent-foreground: #000000; /* Black */
  --destructive: #ef4444; /* Red */
  --destructive-foreground: #ffffff; /* White */
  --border: #d1d5db; /* Gray Border */
  --input: #e5e7eb; /* Input Background */
  --ring: #000000; /* Black Ring */
  --chart-1: #000000;
  --chart-2: #374151;
  --chart-3: #6b7280;
  --chart-4: #9ca3af;
  --chart-5: #d1d5db;
  --sidebar: #ffffff;
  --sidebar-foreground: #000000;
  --sidebar-primary: #000000;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f9fafb;
  --sidebar-accent-foreground: #000000;
  --sidebar-border: #e5e7eb;
  --sidebar-ring: #000000;
}

/* Font Optimization Features */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: 'palt';
}

/* CJK Character Support and Enhanced Typography */
.cjk-text,
.prose p,
.prose li,
.prose table td,
.prose table th,
.markdown-content {
  font-family:
    var(--font-geist-sans),
    ui-sans-serif,
    -apple-system,
    'Segoe UI',
    'Helvetica Neue',
    'Noto Sans',
    'Noto Sans CJK JP',
    'Noto Sans CJK KR',
    'Noto Sans CJK SC',
    'Noto Sans CJK TC',
    sans-serif;
  line-height: 1.7;
}

/* Typography System with Geist Font Weights */
.text-h1 {
  font-family: var(--font-geist-sans);
  font-size: 2rem; /* 32px */
  font-weight: 700;
  line-height: 1.2;
}

.text-h2 {
  font-family: var(--font-geist-sans);
  font-size: 1.75rem; /* 28px */
  font-weight: 600;
  line-height: 1.3;
}

.text-h3 {
  font-family: var(--font-geist-sans);
  font-size: 1.5rem; /* 24px */
  font-weight: 600;
  line-height: 1.3;
}

.text-h4 {
  font-family: var(--font-geist-sans);
  font-size: 1.25rem; /* 20px */
  font-weight: 500;
  line-height: 1.4;
}

.text-h5 {
  font-family: var(--font-geist-sans);
  font-size: 1.125rem; /* 18px */
  font-weight: 500;
  line-height: 1.4;
}

.text-h6 {
  font-family: var(--font-geist-sans);
  font-size: 1rem; /* 16px */
  font-weight: 500;
  line-height: 1.5;
}

/* Map common Tailwind classes to our typography system */
.text-xl {
  font-family: var(--font-geist-sans);
  font-size: 1.125rem; /* 18px - body-large */
  font-weight: 400;
  line-height: 1.6;
}

.text-lg {
  font-family: var(--font-geist-sans);
  font-size: 1.125rem; /* 18px - body-large */
  font-weight: 400;
  line-height: 1.6;
}

.text-sm {
  font-family: var(--font-geist-sans);
  font-size: 0.875rem; /* 14px - body-small */
  font-weight: 400;
  line-height: 1.5;
}

.text-xs {
  font-family: var(--font-geist-sans);
  font-size: 0.75rem; /* 12px - caption */
  font-weight: 400;
  line-height: 1.4;
}

/* Override Tailwind font weights to use our system */
.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

.text-body-large {
  font-family: var(--font-geist-sans);
  font-size: 1.125rem; /* 18px */
  font-weight: 400;
  line-height: 1.6;
}

.text-body {
  font-family: var(--font-geist-sans);
  font-size: 1rem; /* 16px */
  font-weight: 400;
  line-height: 1.6;
}

.text-body-small {
  font-family: var(--font-geist-sans);
  font-size: 0.875rem; /* 14px */
  font-weight: 400;
  line-height: 1.5;
}

.text-button {
  font-family: var(--font-geist-sans);
  font-size: 1rem; /* 16px */
  font-weight: 500;
  line-height: 1.5;
}

.text-label {
  font-family: var(--font-geist-sans);
  font-size: 0.875rem; /* 14px */
  font-weight: 500;
  line-height: 1.4;
}

.text-caption {
  font-family: var(--font-geist-sans);
  font-size: 0.75rem; /* 12px */
  font-weight: 400;
  line-height: 1.4;
}

/* Ensure all text uses Geist font family */
* {
  font-family: var(--font-geist-sans), ui-sans-serif, -apple-system, 'Segoe UI', 'Helvetica Neue', 'Noto Sans', 'Noto Sans CJK JP', 'Noto Sans CJK KR',
    'Noto Sans CJK SC', 'Noto Sans CJK TC', sans-serif;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    transition: background-color 0.3s ease, color 0.3s ease;
  }
}
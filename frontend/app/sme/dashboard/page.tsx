"use client";

import AppHeader from "@/components/navigation/AppHeader";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { DashboardCard, QuickAction } from "@/components/ui/dashboard-card";
import { useAuthStore } from "@/stores/authStore";
import { motion } from "framer-motion";
import {
    ArrowRight,
    BarChart3,
    Check,
    Clock,
    FileText,
    Target,
    TrendingUp,
    Upload,
    Users
} from "lucide-react";
import { useRouter } from "next/navigation";

export default function SMEDashboard() {
  const router = useRouter();
  const { logout, user } = useAuthStore();

  const handleLogout = () => {
    logout();
    router.push('/');
  };

  // Mock data - in real app, this would come from API
  const documentsUploaded = 8;
  const totalDocuments = 12;
  const profileCompletion = 85;

  const recentActivity = [
    { id: 1, type: "document", message: "Financial statements uploaded", time: "2 hours ago", status: "success" },
    { id: 2, type: "report", message: "Report request submitted to admin", time: "1 day ago", status: "pending" },
    { id: 3, type: "meeting", message: "Advisor call scheduled for tomorrow", time: "2 days ago", status: "success" },
    { id: 4, type: "document", message: "Tax returns uploaded successfully", time: "3 days ago", status: "success" }
  ];

  const actionItems = [
    { id: 1, task: "Upload required documents for report generation", priority: "high", completed: false },
    { id: 2, task: "Schedule consultation call", priority: "medium", completed: false },
    { id: 3, task: "Review shared reports from admin", priority: "medium", completed: false }
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-red-300 bg-red-50';
      case 'medium': return 'border-amber-300 bg-amber-50';
      case 'low': return 'border-gray-300 bg-gray-50';
      default: return 'border-gray-300 bg-gray-50';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <AppHeader variant="sme" />

      <div className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-black mb-2">
                Welcome back, {user?.companyName || user?.name || 'User'}
              </h1>
              <p className="text-gray-600">Here's your business performance overview</p>
            </div>
            <Badge variant="default" className="bg-black text-white">
              Profile {profileCompletion}% Complete
            </Badge>
          </div>
        </motion.div>

        {/* Key Actions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* Documents Status */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <FileText className="w-6 h-6 text-blue-600" />
                  </div>
                  <Badge className="bg-blue-100 text-blue-700 border-blue-200">
                    {documentsUploaded}/{totalDocuments}
                  </Badge>
                </div>
                <h3 className="text-lg font-semibold text-black mb-2">Documents</h3>
                <p className="text-sm text-gray-600 mb-4">Upload required documents for report generation</p>
                <Button 
                  onClick={() => router.push('/sme/dashboard/upload')}
                  className="w-full !bg-black !text-white"
                  size="sm"
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Documents
                </Button>
              </CardContent>
            </Card>
          </motion.div>

          {/* Request Report */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <BarChart3 className="w-6 h-6 text-green-600" />
                  </div>
                  <Badge className="bg-green-100 text-green-700 border-green-200">
                    Available
                  </Badge>
                </div>
                <h3 className="text-lg font-semibold text-black mb-2">Request Report</h3>
                <p className="text-sm text-gray-600 mb-4">Submit a request for admin to generate your report</p>
                <Button 
                  onClick={() => router.push('/sme/dashboard/reports')}
                  className="w-full !bg-black !text-white"
                  size="sm"
                >
                  <FileText className="w-4 h-4 mr-2" />
                  Request Report
                </Button>
              </CardContent>
            </Card>
          </motion.div>

          {/* Schedule Call */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Users className="w-6 h-6 text-purple-600" />
                  </div>
                  <Badge className="bg-purple-100 text-purple-700 border-purple-200">
                    Book Now
                  </Badge>
                </div>
                <h3 className="text-lg font-semibold text-black mb-2">Schedule Call</h3>
                <p className="text-sm text-gray-600 mb-4">Book a consultation call with our advisors</p>
                <Button 
                  onClick={() => router.push('/sme/dashboard/advisor-call')}
                  className="w-full !bg-black !text-white"
                  size="sm"
                >
                  <Users className="w-4 h-4 mr-2" />
                  Schedule Call
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Action Items */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-black flex items-center">
                  <Check className="w-5 h-5 mr-2" />
                  Action Items
                </CardTitle>
                <CardDescription className="text-gray-600">
                  Complete these tasks to improve your financial health score
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {actionItems.map((item) => (
                    <div
                      key={item.id}
                      className={`flex items-center justify-between p-4 rounded-lg border ${
                        item.completed ? "bg-green-50 border-green-200" : getPriorityColor(item.priority)
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        {item.completed ? (
                          <div className="w-5 h-5 bg-green-600 rounded-full flex items-center justify-center">
                            <Check className="w-3 h-3 text-white" />
                          </div>
                        ) : (
                          <div className={`w-5 h-5 rounded-full border-2 ${
                            item.priority === "high" ? "border-red-500" :
                            item.priority === "medium" ? "border-amber-500" : "border-gray-400"
                          }`} />
                        )}
                        <div>
                          <p className={`text-sm font-medium ${
                            item.completed ? "text-green-800" : "text-gray-900"
                          }`}>
                            {item.task}
                          </p>
                          <p className="text-xs text-gray-600 capitalize">
                            {item.priority} priority
                          </p>
                        </div>
                      </div>
                      {!item.completed && (
                        <Button 
                          variant="outline-gray" 
                          size="sm"
                          className="shrink-0"
                        >
                          <ArrowRight className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-black flex items-center">
                  <Clock className="w-5 h-5 mr-2" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3">
                      <div className={`w-2 h-2 rounded-full mt-2 ${
                        activity.status === "success" ? "bg-green-500" :
                        activity.status === "info" ? "bg-blue-500" : "bg-amber-500"
                      }`} />
                      <div className="flex-1">
                        <p className="text-black text-sm font-medium">{activity.message}</p>
                        <p className="text-gray-500 text-xs">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.7 }}
          className="mt-8"
        >
          <Card className="bg-white border-gray-200 shadow-sm">
            <CardHeader>
              <CardTitle className="text-black">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <QuickAction
                  label="Upload Documents"
                  icon={<Upload className="w-4 h-4" />}
                  href="/sme/dashboard/upload"
                  variant="primary"
                />
                <QuickAction
                  label="View Reports"
                  icon={<BarChart3 className="w-4 h-4" />}
                  href="/sme/dashboard/reports"
                  variant="secondary"
                />
                <QuickAction
                  label="Schedule Call"
                  icon={<Users className="w-4 h-4" />}
                  href="/sme/dashboard/advisor-call"
                  variant="secondary"
                />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}

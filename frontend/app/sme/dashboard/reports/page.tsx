"use client";

import AppHeader from "@/components/navigation/AppHeader";
import BackButton from "@/components/navigation/BackButton";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuthStore } from "@/stores/authStore";
import { motion } from "framer-motion";
import {
    BarChart3,
    Calendar,
    Download,
    Eye,
    FileText,
    Target,
    TrendingDown,
    TrendingUp,
    Users
} from "lucide-react";

export default function ReportsPage() {
  const { user } = useAuthStore();

  const sharedReports = [
    {
      id: 1,
      title: "Financial Health Score Report",
      description: "Comprehensive analysis of your business financial health",
      sharedBy: "Admin",
      sharedDate: "2 days ago",
      status: "available",
      icon: BarChart3
    },
    {
      id: 2,
      title: "Growth Analysis Report",
      description: "Market opportunities and growth projections",
      sharedBy: "Admin",
      sharedDate: "1 week ago",
      status: "available",
      icon: Target
    }
  ];

  const reportRequests = [
    {
      id: 1,
      reportType: "Financial Health Score",
      requestDate: "1 day ago",
      status: "pending"
    },
    {
      id: 2,
      reportType: "Market Analysis",
      requestDate: "3 days ago",
      status: "processing"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      <AppHeader variant="sme" />

      {/* Back Navigation */}
      <BackButton href="/sme/dashboard" label="Back to Dashboard" />

      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="mb-4">
            <h1 className="text-3xl font-bold text-black mb-2">Reports</h1>
            <p className="text-gray-600">View shared reports and request new reports from admin</p>
          </div>
        </motion.div>

        {/* Request Report Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="mb-8"
        >
          <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
            <CardContent className="p-8">
              <div className="text-center">
                <FileText className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-black mb-2">Request New Report</h3>
                <p className="text-gray-700 mb-6 max-w-2xl mx-auto">
                  Submit a request to admin for report generation. Admin will process your request and share the report with you.
                </p>
                <div className="flex justify-center gap-4">
                  <Button className="bg-black hover:bg-gray-800 text-white">
                    <BarChart3 className="w-4 h-4 mr-2" />
                    Request Financial Health Report
                  </Button>
                  <Button variant="outline" className="border-black text-black hover:bg-gray-100">
                    <Target className="w-4 h-4 mr-2" />
                    Request Growth Analysis
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Report Requests Status */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mb-8"
        >
          <Card className="bg-white border-gray-200 shadow-sm">
            <CardHeader>
              <CardTitle className="text-black">Your Report Requests</CardTitle>
              <CardDescription className="text-gray-600">
                Track the status of your report requests
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportRequests.map((request) => (
                  <div key={request.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="font-semibold text-black">{request.reportType}</h4>
                      <p className="text-sm text-gray-600">Requested {request.requestDate}</p>
                    </div>
                    <Badge className={request.status === "pending" ? "bg-yellow-100 text-yellow-700 border-yellow-200" : "bg-blue-100 text-blue-700 border-blue-200"}>
                      {request.status === "pending" ? "Pending Admin Review" : "Processing"}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Shared Reports */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Card className="bg-white border-gray-200 shadow-sm">
            <CardHeader>
              <CardTitle className="text-black">Reports Shared by Admin</CardTitle>
              <CardDescription className="text-gray-600">
                View and download reports that have been shared with you
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {sharedReports.map((report) => (
                  <div key={report.id} className="bg-gray-50 rounded-lg p-6 hover:bg-gray-100 transition-colors">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center">
                        <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center mr-4">
                          <report.icon className="w-6 h-6 text-black" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-black">{report.title}</h4>
                          <p className="text-sm text-gray-600 mt-1">{report.description}</p>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between mt-4 pt-4 border-t border-gray-200">
                      <div className="text-sm text-gray-600">
                        <p>Shared by {report.sharedBy}</p>
                        <p className="text-xs">{report.sharedDate}</p>
                      </div>
                      <div className="flex space-x-2">
                        <Button size="sm" className="!bg-black !text-white">
                          <Eye className="w-4 h-4 mr-1" />
                          View
                        </Button>
                        <Button size="sm" variant="outline">
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              {sharedReports.length === 0 && (
                <div className="text-center py-12">
                  <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-black mb-2">No reports shared yet</h3>
                  <p className="text-gray-600">Request a report and admin will share it with you once generated</p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>

      </div>
    </div>
  );
}

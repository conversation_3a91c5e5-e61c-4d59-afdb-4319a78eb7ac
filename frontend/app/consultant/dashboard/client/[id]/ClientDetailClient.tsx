"use client";

import AppHeader from "@/components/navigation/AppHeader";
import BackButton from "@/components/navigation/BackButton";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuthStore } from "@/stores/authStore";
import {
    Building,
    Calendar,
    Download,
    FileText,
    Mail,
    MessageCircle,
    Phone,
    TrendingUp,
    User
} from "lucide-react";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";

interface ClientDetail {
  id: number;
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  industry: string;
  location: string;
  revenue: string;
  employees: string;
  status: string;
  engagementType: string;
  startDate: string;
  nextMeeting: string;
  description: string;
  challenges: string[];
  objectives: string[];
  progress: {
    completed: number;
    total: number;
  };
  recentActivities: Array<{
    date: string;
    activity: string;
    type: string;
  }>;
  documents: string[];
}

export default function ClientDetailClient() {
  const { user } = useAuthStore();
  const params = useParams();
  const clientId = params.id as string;
  const [client, setClient] = useState<ClientDetail | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Mock data - replace with actual API call
    const mockClient: ClientDetail = {
      id: parseInt(clientId),
      companyName: "TechStart Solutions",
      contactPerson: "Sarah Johnson",
      email: "<EMAIL>",
      phone: "+****************",
      industry: "Technology",
      location: "San Francisco, CA",
      revenue: "$2.5M",
      employees: "25-50",
      status: "active",
      engagementType: "Financial Strategy",
      startDate: "2024-01-15",
      nextMeeting: "2024-12-22T14:00:00",
      description: "TechStart Solutions is a growing SaaS company focused on project management tools for small businesses.",
      challenges: [
        "Cash flow management",
        "Scaling operations",
        "Investment readiness",
        "Financial reporting automation"
      ],
      objectives: [
        "Improve cash flow by 30%",
        "Prepare for Series A funding",
        "Implement automated financial reporting",
        "Optimize operational costs"
      ],
      progress: {
        completed: 7,
        total: 12
      },
      recentActivities: [
        {
          date: "2024-12-18",
          activity: "Quarterly financial review completed",
          type: "meeting"
        },
        {
          date: "2024-12-15",
          activity: "Cash flow projection updated",
          type: "document"
        },
        {
          date: "2024-12-10",
          activity: "Investor pitch deck reviewed",
          type: "review"
        }
      ],
      documents: [
        "Financial Statements Q4 2024",
        "Cash Flow Projections",
        "Investor Pitch Deck",
        "Operational Cost Analysis"
      ]
    };

    setTimeout(() => {
      setClient(mockClient);
      setLoading(false);
    }, 1000);
  }, [clientId]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-gray-100 text-black border-gray-300";
      case "pending": return "bg-gray-200 text-gray-800 border-gray-400";
      case "completed": return "bg-black text-white border-black";
      default: return "bg-gray-50 text-gray-600 border-gray-200";
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-black">Loading client details...</div>
      </div>
    );
  }

  if (!client) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-white">Client not found</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <AppHeader variant="consultant" />

      <div className="container mx-auto px-6 py-8">
        {/* Back Navigation */}
        <BackButton href="/consultant/dashboard" label="Back to Dashboard" />

        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <div>
              <h1 className="text-3xl font-bold text-black">{client.companyName}</h1>
              <p className="text-gray-600">{client.industry} • {client.location}</p>
            </div>
          </div>
          <Badge className={getStatusColor(client.status)}>
            {client.status}
          </Badge>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Main Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Client Overview */}
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-black">Client Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">{client.description}</p>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-gray-600 text-sm">Contact Person</p>
                    <p className="text-black font-semibold">{client.contactPerson}</p>
                  </div>
                  <div>
                    <p className="text-gray-600 text-sm">Engagement Type</p>
                    <p className="text-black font-semibold">{client.engagementType}</p>
                  </div>
                  <div>
                    <p className="text-gray-600 text-sm">Start Date</p>
                    <p className="text-black font-semibold">{new Date(client.startDate).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <p className="text-gray-600 text-sm">Next Meeting</p>
                    <p className="text-black font-semibold">{new Date(client.nextMeeting).toLocaleDateString()}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Challenges & Objectives */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="bg-white border-gray-200 shadow-sm">
                <CardHeader>
                  <CardTitle className="text-black">Key Challenges</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {client.challenges.map((challenge, index) => (
                      <li key={index} className="text-gray-600 flex items-start">
                        <span className="w-2 h-2 bg-red-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        {challenge}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>

              <Card className="bg-white border-gray-200 shadow-sm">
                <CardHeader>
                  <CardTitle className="text-black">Objectives</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {client.objectives.map((objective, index) => (
                      <li key={index} className="text-gray-600 flex items-start">
                        <span className="w-2 h-2 bg-green-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                        {objective}
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activities */}
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-black">Recent Activities</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {client.recentActivities.map((activity, index) => (
                    <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                      <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
                        {activity.type === 'meeting' && <Calendar className="w-4 h-4 text-black" />}
                        {activity.type === 'document' && <FileText className="w-4 h-4 text-black" />}
                        {activity.type === 'review' && <TrendingUp className="w-4 h-4 text-black" />}
                      </div>
                      <div className="flex-1">
                        <p className="text-black font-medium">{activity.activity}</p>
                        <p className="text-gray-600 text-sm">{new Date(activity.date).toLocaleDateString()}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Key Metrics & Actions */}
          <div className="space-y-6">
            {/* Contact Information */}
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-black flex items-center">
                  <User className="w-5 h-5 mr-2" />
                  Contact Info
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <Mail className="w-4 h-4 text-gray-600" />
                    <span className="text-gray-600">{client.email}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="w-4 h-4 text-gray-600" />
                    <span className="text-gray-600">{client.phone}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Building className="w-4 h-4 text-gray-600" />
                    <span className="text-gray-600">{client.location}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Company Metrics */}
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-black">Company Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Revenue</span>
                    <span className="text-green-600 font-semibold">{client.revenue}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Employees</span>
                    <span className="text-black font-semibold">{client.employees}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Industry</span>
                    <span className="text-black font-semibold">{client.industry}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Progress */}
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-black">Engagement Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Completed</span>
                    <span className="text-black font-semibold">{client.progress.completed}/{client.progress.total}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-black h-2 rounded-full" 
                      style={{ width: `${(client.progress.completed / client.progress.total) * 100}%` }}
                    ></div>
                  </div>
                  <p className="text-gray-600 text-sm">
                    {Math.round((client.progress.completed / client.progress.total) * 100)}% Complete
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-black">Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button className="w-full bg-black hover:bg-gray-800">
                    <MessageCircle className="w-4 h-4 mr-2" />
                    Send Message
                  </Button>
                  <Button variant="outline-gray" className="w-full">
                    <Calendar className="w-4 h-4 mr-2" />
                    Schedule Meeting
                  </Button>
                  <Button variant="outline-gray" className="w-full">
                    <Download className="w-4 h-4 mr-2" />
                    Download Reports
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

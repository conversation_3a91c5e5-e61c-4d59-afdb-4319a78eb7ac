"use client";

import AppHeader from "@/components/navigation/AppHeader";
import { AdvisorList } from "@/components/ui/advisor-card";
import { useState } from "react";

export default function AdvisorSelectionPage() {
  const [selectedId, setSelectedId] = useState<string | null>(null);

  const advisorData = [
    {
      id: "1",
      name: "Dr. <PERSON>",
      title: "Senior Financial Consultant",
      rating: 4.9,
      reviews: 120,
      specialties: ["Startup Funding", "Growth Strategy", "Valuation"],
      imageUrl: "https://randomuser.me/api/portraits/men/1.jpg",
    },
    {
      id: "2",
      name: "Ms. <PERSON>",
      title: "Investment Readiness Advisor",
      rating: 4.7,
      reviews: 85,
      specialties: ["Investor Relations", "Pitch Deck", "Due Diligence"],
      imageUrl: "https://randomuser.me/api/portraits/women/2.jpg",
    },
    {
      id: "3",
      name: "Mr. <PERSON>",
      title: "Business Optimization Expert",
      rating: 4.8,
      reviews: 95,
      specialties: ["Cost Reduction", "Operational Efficiency", "Market Analysis"],
      imageUrl: "https://randomuser.me/api/portraits/men/3.jpg",
    },
    {
      id: "4",
      name: "Dr. <PERSON>",
      title: "Strategic Financial Planner",
      rating: 4.9,
      reviews: 110,
      specialties: ["Long-term Planning", "Risk Management", "Mergers & Acquisitions"],
      imageUrl: "https://randomuser.me/api/portraits/women/4.jpg",
    },
  ];

  const handleViewProfile = (id: string) => {
    console.log(`Viewing profile for advisor: ${id}`);
    // In a real app, you would navigate to a detailed advisor profile page
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <AppHeader variant="consultant" />

      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-black mb-6">Select Your Advisor</h1>
        <AdvisorList
          advisors={advisorData}
          selectedAdvisorId={selectedId}
          onSelectAdvisor={setSelectedId}
          onViewProfile={handleViewProfile}
        />
      </div>
    </div>
  );
}
"use client";

import AppHeader from "@/components/navigation/AppHeader";
import BackButton from "@/components/navigation/BackButton";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { motion } from "framer-motion";
import { Building, Calendar, Mail, Phone, Search, TrendingUp, Users } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface Client {
  id: number;
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  industry: string;
  location: string;
  revenue: string;
  employees: string;
  status: string;
  engagementType: string;
  startDate: string;
  nextMeeting: string;
  progress: {
    completed: number;
    total: number;
  };
}

export default function ConsultantClients() {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");

  // Mock data - replace with actual API call
  const clients: Client[] = [
    {
      id: 1,
      companyName: "TechCorp Solutions",
      contact<PERSON><PERSON>: "<PERSON>",
      email: "<EMAIL>",
      phone: "+91 98765 43210",
      industry: "Technology",
      location: "Bangalore",
      revenue: "$3.2M",
      employees: "25-50",
      status: "active",
      engagementType: "Financial Planning",
      startDate: "2024-01-15",
      nextMeeting: "2024-02-15",
      progress: { completed: 7, total: 10 }
    },
    {
      id: 2,
      companyName: "GreenEnergy Ltd",
      contactPerson: "Sarah Johnson",
      email: "<EMAIL>",
      phone: "+91 87654 32109",
      industry: "Renewable Energy",
      location: "Mumbai",
      revenue: "$1.8M",
      employees: "15-25",
      status: "pending",
      engagementType: "Growth Strategy",
      startDate: "2024-02-01",
      nextMeeting: "2024-02-20",
      progress: { completed: 3, total: 8 }
    },
    {
      id: 3,
      companyName: "HealthTech Innovations",
      contactPerson: "Dr. Raj Patel",
      email: "<EMAIL>",
      phone: "+91 76543 21098",
      industry: "Healthcare",
      location: "Delhi",
      revenue: "$2.5M",
      employees: "30-40",
      status: "completed",
      engagementType: "Market Analysis",
      startDate: "2023-11-01",
      nextMeeting: "Follow-up scheduled",
      progress: { completed: 12, total: 12 }
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-green-500/20 text-green-400 border-green-500/30";
      case "pending": return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30";
      case "completed": return "bg-blue-500/20 text-blue-400 border-blue-500/30";
      default: return "bg-slate-500/20 text-slate-400 border-slate-500/30";
    }
  };

  const filteredClients = clients.filter(client => {
    const matchesSearch = client.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         client.industry.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === "all" || client.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <header className="border-b border-gray-200 bg-white/80 backdrop-blur-sm sticky top-0 z-50" />
      
      {/* Back Navigation */}
      <BackButton href="/consultant/dashboard" label="Back to Dashboard" />

      <div className="container mx-auto px-6 py-8">
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-black mb-2">My Clients</h1>
            <p className="text-gray-600">Manage your client engagements and track progress</p>
          </div>
          <div className="flex items-center space-x-4">
            <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
              {filteredClients.length} Clients
            </Badge>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4 mb-8">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-4 h-4" />
            <Input
              placeholder="Search clients..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-white border-gray-300 text-black"
            />
          </div>
          <div className="flex gap-2">
            {["all", "active", "pending", "completed"].map((status) => (
              <Button
                key={status}
                variant={filterStatus === status ? "primary-black" : "outline-gray"}
                size="sm"
                onClick={() => setFilterStatus(status)}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </Button>
            ))}
          </div>
        </div>

        {/* Clients Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredClients.map((client, index) => (
            <motion.div
              key={client.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Card className="bg-white border-gray-200 hover:border-gray-400 transition-colors">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                        <Building className="w-5 h-5 text-black" />
                      </div>
                      <div>
                        <CardTitle className="text-black text-lg">{client.companyName}</CardTitle>
                        <p className="text-gray-600 text-sm">{client.industry}</p>
                      </div>
                    </div>
                    <Badge className={getStatusColor(client.status)}>
                      {client.status}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2 text-sm">
                      <Users className="w-4 h-4 text-gray-600" />
                      <span className="text-gray-600">{client.contactPerson}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm">
                      <Mail className="w-4 h-4 text-gray-600" />
                      <span className="text-gray-600">{client.email}</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm">
                      <TrendingUp className="w-4 h-4 text-gray-600" />
                      <span className="text-gray-600">{client.revenue} revenue</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Progress</span>
                      <span className="text-gray-600">{client.progress.completed}/{client.progress.total}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-black h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(client.progress.completed / client.progress.total) * 100}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="flex gap-2 pt-2">
                    <Link href={`/consultant/dashboard/client/${client.id}`} className="flex-1">
                      <Button size="sm" className="w-full">
                        View Details
                      </Button>
                    </Link>
                    <Button variant="outline-gray" size="sm">
                      <Calendar className="w-4 h-4" />
                    </Button>
                    <Button variant="outline-gray" size="sm">
                      <Phone className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {filteredClients.length === 0 && (
  <div className="text-center py-12">
    <Building className="w-16 h-16 text-gray-600 mx-auto mb-4" />
    <h3 className="text-xl font-semibold text-black mb-2">No clients found</h3>
    <p className="text-gray-600">Try adjusting your search or filter criteria</p>
  </div>
)}
      </div>
    </div>
  );
}

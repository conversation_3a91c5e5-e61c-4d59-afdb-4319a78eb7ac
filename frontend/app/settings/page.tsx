"use client";

import AppHeader from "@/components/navigation/AppHeader";
import BackButton from "@/components/navigation/BackButton";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useAuthStore } from "@/stores/authStore";
import { motion } from "framer-motion";
import {
    Bell,
    CreditCard,
    Eye,
    EyeOff,
    Lock,
    Save,
    Shield,
    User
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";

export default function SettingsPage() {
  const { user, updateProfile } = useAuthStore();
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [activeTab, setActiveTab] = useState("profile");

  const [formData, setFormData] = useState({
    name: user?.name || "",
    email: user?.email || "",
    companyName: user?.companyName || "",
    phone: "",
    address: "",
    website: "",
    description: "",
    currentPassword: "",
    newPassword: "",
    confirmPassword: ""
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSaveProfile = () => {
    updateProfile({
      name: formData.name,
      companyName: formData.companyName
    });
    // Show success message
  };

  const tabs = [
    { id: "profile", label: "Profile", icon: User },
    { id: "security", label: "Security", icon: Shield },
    { id: "notifications", label: "Notifications", icon: Bell },
    { id: "billing", label: "Billing", icon: CreditCard }
  ];

  const getDashboardPath = () => {
    switch (user?.role) {
      case 'investor': return '/investor/dashboard';
      case 'consultant': return '/consultant/dashboard';
      default: return '/sme/dashboard';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <AppHeader />

      {/* Back Navigation */}
      <BackButton />

      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold text-black mb-2">Account Settings</h1>
              <p className="text-gray-600">Manage your account preferences and security settings</p>
            </div>
            <Badge variant="outline">
              {user?.role?.toUpperCase()} Account
            </Badge>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardContent className="p-4">
                <div className="space-y-2">
                  {tabs.map((tab) => (
                    <Button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      variant={activeTab === tab.id ? "default" : "ghost"}
                      className={`w-full justify-start ${
                        activeTab === tab.id
                          ? "bg-black text-white hover:bg-gray-800"
                          : "text-gray-600 hover:text-black hover:bg-gray-50"
                      }`}
                    >
                      <tab.icon className="w-4 h-4 mr-3" />
                      {tab.label}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Content */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="lg:col-span-3"
          >
            {activeTab === "profile" && (
              <Card className="bg-white border-gray-200 shadow-sm">
  <CardHeader>
    <CardTitle className="text-black flex items-center">
      <User className="w-5 h-5 mr-2" />
      Profile Information
    </CardTitle>
    <CardDescription className="text-gray-600">
      Update your personal and business information
    </CardDescription>
  </CardHeader>
  <CardContent className="space-y-6">
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <Label className="text-black">Full Name</Label>
        <Input
          value={formData.name}
          onChange={(e) => handleInputChange("name", e.target.value)}
          className="bg-white border-gray-300 text-black mt-2"
        />
      </div>
      <div>
        <Label className="text-black">Email Address</Label>
        <Input
          value={formData.email}
          onChange={(e) => handleInputChange("email", e.target.value)}
          className="bg-white border-gray-300 text-black mt-2"
          disabled
        />
      </div>
    </div>

    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <Label className="text-black">Company Name</Label>
        <Input
          value={formData.companyName}
          onChange={(e) => handleInputChange("companyName", e.target.value)}
          className="bg-white border-gray-300 text-black mt-2"
        />
      </div>
      <div>
        <Label className="text-black">Phone Number</Label>
        <Input
          value={formData.phone}
          onChange={(e) => handleInputChange("phone", e.target.value)}
          className="bg-white border-gray-300 text-black mt-2"
          placeholder="+****************"
        />
      </div>
    </div>

    <div>
      <Label className="text-black">Business Description</Label>
      <Textarea
        value={formData.description}
        onChange={(e) => handleInputChange("description", e.target.value)}
        className="bg-white border-gray-300 text-black mt-2"
        placeholder="Brief description of your business..."
        rows={3}
      />
    </div>

    <Button onClick={handleSaveProfile} className="bg-black hover:bg-gray-800">
      <Save className="w-4 h-4 mr-2" />
      Save Changes
    </Button>
  </CardContent>
</Card>
            )}

            {activeTab === "security" && (
              <Card className="bg-white border-gray-200 shadow-sm">
  <CardHeader>
    <CardTitle className="text-black flex items-center">
      <Shield className="w-5 h-5 mr-2" />
      Security Settings
    </CardTitle>
    <CardDescription className="text-gray-600">
      Manage your password and security preferences
    </CardDescription>
  </CardHeader>
  <CardContent className="space-y-6">
    <div>
      <Label className="text-black">Current Password</Label>
      <div className="relative mt-2">
        <Input
          type={showPassword ? "text" : "password"}
          value={formData.currentPassword}
          onChange={(e) => handleInputChange("currentPassword", e.target.value)}
          className="bg-white border-gray-300 text-black pr-10"
        />
        <button
          type="button"
          onClick={() => setShowPassword(!showPassword)}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-600 hover:text-black"
        >
          {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
        </button>
      </div>
    </div>

    <div>
      <Label className="text-black">New Password</Label>
      <Input
        type="password"
        value={formData.newPassword}
        onChange={(e) => handleInputChange("newPassword", e.target.value)}
        className="bg-white border-gray-300 text-black mt-2"
      />
    </div>

    <div>
      <Label className="text-black">Confirm New Password</Label>
      <Input
        type="password"
        value={formData.confirmPassword}
        onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
        className="bg-white border-gray-300 text-black mt-2"
      />
    </div>

    <Button className="bg-black hover:bg-gray-800">
      <Lock className="w-4 h-4 mr-2" />
      Update Password
    </Button>
  </CardContent>
</Card>
            )}

            {activeTab === "notifications" && (
              <Card className="bg-white border-gray-200 shadow-sm">
  <CardHeader>
    <CardTitle className="text-black flex items-center">
      <Bell className="w-5 h-5 mr-2" />
      Notification Preferences
    </CardTitle>
    <CardDescription className="text-gray-600">
      Choose what notifications you want to receive
    </CardDescription>
  </CardHeader>
  <CardContent>
    <div className="space-y-4">
      <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
        <div>
          <p className="text-black font-semibold">Email Notifications</p>
          <p className="text-gray-600 text-sm">Receive updates via email</p>
        </div>
        <Button variant="outline-gray">
          Enable
        </Button>
      </div>
      <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
        <div>
          <p className="text-black font-semibold">Investor Updates</p>
          <p className="text-gray-600 text-sm">Get notified when investors view your profile</p>
        </div>
        <Button variant="outline-gray">
          Enable
        </Button>
      </div>
      <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
        <div>
          <p className="text-black font-semibold">Score Changes</p>
          <p className="text-gray-600 text-sm">Notifications when your financial score changes</p>
        </div>
        <Button variant="outline-gray">
          Enable
        </Button>
      </div>
    </div>
  </CardContent>
</Card>
            )}

            {activeTab === "billing" && (
              <Card className="bg-white border-gray-200 shadow-sm">
  <CardHeader>
    <CardTitle className="text-black flex items-center">
      <CreditCard className="w-5 h-5 mr-2" />
      Billing & Subscription
    </CardTitle>
    <CardDescription className="text-gray-600">
      Manage your subscription and billing information
    </CardDescription>
  </CardHeader>
  <CardContent>
    <div className="space-y-6">
      <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-black font-semibold">Professional Plan</p>
            <p className="text-gray-600 text-sm">$39/month • Next billing: Jan 15, 2024</p>
          </div>
          <Badge variant="default">
            Active
          </Badge>
        </div>
      </div>

      <div className="flex space-x-4">
        <Link href="/pricing">
          <Button variant="outline-gray">
            Change Plan
          </Button>
        </Link>
        <Button variant="outline-gray" className="border-red-600 text-red-600 hover:bg-red-50">
          Cancel Subscription
        </Button>
      </div>
    </div>
  </CardContent>
</Card>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
}

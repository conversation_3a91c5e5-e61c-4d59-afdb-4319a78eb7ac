// Common form constants and options used across all signup forms

export const countries = [
  { code: "IN", name: "India", phoneCode: "+91" },
  { code: "AE", name: "UAE", phoneCode: "+971" },
  { code: "US", name: "United States", phoneCode: "+1" },
  { code: "GB", name: "United Kingdom", phoneCode: "+44" },
  { code: "CA", name: "Canada", phoneCode: "+1" },
  { code: "AU", name: "Australia", phoneCode: "+61" },
  { code: "SG", name: "Singapore", phoneCode: "+65" },
  { code: "DE", name: "Germany", phoneCode: "+49" },
  { code: "FR", name: "France", phoneCode: "+33" },
  { code: "<PERSON>", name: "Japan", phoneCode: "+81" },
];

export const getStatesByCountry = (countryCode: string) => {
  const stateData: any = {
    "IN": [
      { code: "MH", name: "Maharashtra" },
      { code: "DL", name: "Delhi" },
      { code: "K<PERSON>", name: "Karnataka" },
      { code: "TG", name: "Telangana" },
      { code: "TN", name: "Tamil Nadu" },
      { code: "G<PERSON>", name: "Gujarat" },
      { code: "RJ", name: "Rajasthan" },
      { code: "UP", name: "Uttar Pradesh" },
      { code: "WB", name: "West Bengal" },
      { code: "AP", name: "Andhra Pradesh" },
      { code: "BR", name: "Bihar" },
      { code: "CT", name: "Chhattisgarh" },
      { code: "GA", name: "Goa" },
      { code: "HR", name: "Haryana" },
      { code: "HP", name: "Himachal Pradesh" },
      { code: "JH", name: "Jharkhand" },
      { code: "KL", name: "Kerala" },
      { code: "MP", name: "Madhya Pradesh" },
      { code: "MN", name: "Manipur" },
      { code: "ML", name: "Meghalaya" },
      { code: "MZ", name: "Mizoram" },
      { code: "NL", name: "Nagaland" },
      { code: "OR", name: "Odisha" },
      { code: "PB", name: "Punjab" },
      { code: "SK", name: "Sikkim" },
      { code: "TR", name: "Tripura" },
      { code: "UT", name: "Uttarakhand" },
    ],
    "AE": [
      { code: "DU", name: "Dubai" },
      { code: "AZ", name: "Abu Dhabi" },
      { code: "SH", name: "Sharjah" },
      { code: "AJ", name: "Ajman" },
      { code: "RA", name: "Ras Al Khaimah" },
      { code: "FU", name: "Fujairah" },
      { code: "UQ", name: "Umm Al Quwain" },
    ],
    "US": [
      { code: "CA", name: "California" },
      { code: "NY", name: "New York" },
      { code: "TX", name: "Texas" },
      { code: "FL", name: "Florida" },
      { code: "IL", name: "Illinois" },
      { code: "PA", name: "Pennsylvania" },
      { code: "OH", name: "Ohio" },
      { code: "GA", name: "Georgia" },
      { code: "NC", name: "North Carolina" },
      { code: "MI", name: "Michigan" },
    ],
    "GB": [
      { code: "ENG", name: "England" },
      { code: "SCT", name: "Scotland" },
      { code: "WLS", name: "Wales" },
      { code: "NIR", name: "Northern Ireland" },
    ],
    "CA": [
      { code: "ON", name: "Ontario" },
      { code: "QC", name: "Quebec" },
      { code: "BC", name: "British Columbia" },
      { code: "AB", name: "Alberta" },
      { code: "MB", name: "Manitoba" },
      { code: "SK", name: "Saskatchewan" },
    ],
    "AU": [
      { code: "NSW", name: "New South Wales" },
      { code: "VIC", name: "Victoria" },
      { code: "QLD", name: "Queensland" },
      { code: "WA", name: "Western Australia" },
      { code: "SA", name: "South Australia" },
      { code: "TAS", name: "Tasmania" },
    ]
  };
  return stateData[countryCode] || [];
};

export const getCitiesByCountry = (countryCode: string) => {
  const cityData: any = {
    "IN": [
      { name: "Mumbai", stateCode: "MH" },
      { name: "Delhi", stateCode: "DL" },
      { name: "Bangalore", stateCode: "KA" },
      { name: "Hyderabad", stateCode: "TG" },
      { name: "Chennai", stateCode: "TN" },
      { name: "Pune", stateCode: "MH" },
      { name: "Ahmedabad", stateCode: "GJ" },
      { name: "Jaipur", stateCode: "RJ" },
      { name: "Kolkata", stateCode: "WB" },
      { name: "Lucknow", stateCode: "UP" },
      { name: "Kanpur", stateCode: "UP" },
      { name: "Nagpur", stateCode: "MH" },
      { name: "Indore", stateCode: "MP" },
      { name: "Thane", stateCode: "MH" },
      { name: "Bhopal", stateCode: "MP" },
      { name: "Visakhapatnam", stateCode: "AP" },
      { name: "Patna", stateCode: "BR" },
      { name: "Vadodara", stateCode: "GJ" },
      { name: "Ghaziabad", stateCode: "UP" },
      { name: "Ludhiana", stateCode: "PB" },
    ],
    "AE": [
      { name: "Dubai", stateCode: "DU" },
      { name: "Abu Dhabi", stateCode: "AZ" },
      { name: "Sharjah", stateCode: "SH" },
      { name: "Ajman", stateCode: "AJ" },
      { name: "Al Ain", stateCode: "AZ" },
      { name: "Ras Al Khaimah", stateCode: "RA" },
      { name: "Fujairah", stateCode: "FU" },
      { name: "Umm Al Quwain", stateCode: "UQ" },
    ],
    "US": [
      { name: "New York", stateCode: "NY" },
      { name: "Los Angeles", stateCode: "CA" },
      { name: "Chicago", stateCode: "IL" },
      { name: "Houston", stateCode: "TX" },
      { name: "Phoenix", stateCode: "AZ" },
      { name: "Philadelphia", stateCode: "PA" },
      { name: "San Antonio", stateCode: "TX" },
      { name: "San Diego", stateCode: "CA" },
      { name: "Dallas", stateCode: "TX" },
      { name: "San Jose", stateCode: "CA" },
    ],
    "GB": [
      { name: "London", stateCode: "ENG" },
      { name: "Birmingham", stateCode: "ENG" },
      { name: "Manchester", stateCode: "ENG" },
      { name: "Glasgow", stateCode: "SCT" },
      { name: "Edinburgh", stateCode: "SCT" },
      { name: "Cardiff", stateCode: "WLS" },
      { name: "Belfast", stateCode: "NIR" },
    ],
    "CA": [
      { name: "Toronto", stateCode: "ON" },
      { name: "Montreal", stateCode: "QC" },
      { name: "Vancouver", stateCode: "BC" },
      { name: "Calgary", stateCode: "AB" },
      { name: "Edmonton", stateCode: "AB" },
      { name: "Ottawa", stateCode: "ON" },
    ],
    "AU": [
      { name: "Sydney", stateCode: "NSW" },
      { name: "Melbourne", stateCode: "VIC" },
      { name: "Brisbane", stateCode: "QLD" },
      { name: "Perth", stateCode: "WA" },
      { name: "Adelaide", stateCode: "SA" },
      { name: "Hobart", stateCode: "TAS" },
    ]
  };
  return cityData[countryCode] || [];
};

// Industry options for SME signup
export const industryOptions = [
  { value: "agriculture", label: "Agriculture & Agribusiness" },
  { value: "automotive", label: "Automotive & Auto Components" },
  { value: "bfsi", label: "Banking, Financial Services & Insurance (BFSI)" },
  { value: "consumer-goods", label: "Consumer Goods (FMCG, Retail, E-Commerce)" },
  { value: "education", label: "Education & Training" },
  { value: "energy", label: "Energy (Conventional & Renewable)" },
  { value: "healthcare", label: "Healthcare, Pharma & Biotechnology" },
  { value: "hospitality", label: "Hospitality, Travel & Leisure" },
  { value: "it", label: "Information Technology & Software Services" },
  { value: "logistics", label: "Logistics, Supply Chain & Transportation" },
  { value: "manufacturing", label: "Manufacturing & Industrial Products" },
  { value: "media", label: "Media, Entertainment & VFX" },
  { value: "mining", label: "Mining & Natural Resources" },
  { value: "professional", label: "Professional & Business Services" },
  { value: "real-estate", label: "Real Estate & Infrastructure" },
  { value: "telecom", label: "Telecom & Communications" },
  { value: "textiles", label: "Textiles, Apparel & Fashion" },
  { value: "utilities", label: "Utilities & Public Services" },
];

// Consultant type options
export const consultantTypeOptions = [
  { value: "client-acquisition", label: "Client Acquisition / Lead Generating Consultant" },
  { value: "sector-specialist", label: "Sector Specialist" },
  { value: "fte-freelance", label: "FTE / Freelance 10X Growth Hack Consultant" },
  { value: "investment-facilitator", label: "Investment Facilitator" },
];

// Consulting areas options
export const consultingAreaOptions = [
  { value: "client-acquisition", label: "Client Acquisition & Lead Generation" },
  { value: "business-development", label: "Business Development Consultant" },
  { value: "ca-tax-advisory", label: "CA/CPA/Tax Advisory (SME Origination)" },
  { value: "referral-partner", label: "Referral & Partner Development" },
  { value: "sector-specialist", label: "Sector Specialist Consulting" },
  { value: "industry-advisory", label: "Industry-Specific SME Advisory (e.g., Agri, Manufacturing, IT, Healthcare, VFX, etc.)" },
  { value: "market-research", label: "Market Research & Competitive Intelligence" },
  { value: "sector-risk", label: "Sector Risk & Opportunity Mapping" },
  { value: "growth-acceleration", label: "Growth Acceleration Consulting" },
  { value: "growth-hack", label: "10X Growth Hack Consultant (FTE/Freelance)" },
  { value: "corporate-finance", label: "Corporate Finance Advisory" },
  { value: "strategy-operations", label: "Strategy & Operations Consulting" },
  { value: "vc-pe-advisory", label: "Venture Capital / Private Equity Advisory" },
  { value: "investment-banking", label: "Investment Banking / Deal Structuring Advisory" },
  { value: "capital-facilitation", label: "Capital Facilitation & Investment Advisory" },
  { value: "investment-facilitator", label: "Investment Facilitator / Merchant Banking" },
  { value: "fundraising", label: "Fundraising & Investor Relations" },
  { value: "finra-broker", label: "FINRA Broker / Registered Intermediary (Global)" },
  { value: "ma-advisory", label: "M&A Advisory & Deal Syndication" },
];

// Investment stage options for investors
export const investmentStageOptions = [
  { value: "early-growth", label: "Early Growth Capital" },
  { value: "expansion", label: "Expansion Capital" },
  { value: "late-stage", label: "Late Stage / Pre-IPO" },
  { value: "turnaround", label: "Turnaround / Special Situations" },
  { value: "minority-growth", label: "Minority Growth Equity" },
  { value: "buyout", label: "Buyout / Acquisition" },
  { value: "mezzanine", label: "Mezzanine / Structured Finance" },
  { value: "debt", label: "Debt Financing" },
  { value: "strategic", label: "Strategic / Corporate Investment" },
  { value: "impact", label: "Impact / ESG Investment" },
];

// Expected returns options for investors
export const expectedReturnsOptions = [
  { value: "under-10", label: "<10%" },
  { value: "11-15", label: "11% - 15%" },
  { value: "16-20", label: "16% - 20%" },
  { value: "21-25", label: "21% - 25%" },
  { value: "custom", label: "Custom" },
];

export const getPhoneCodeByCountry = (countryCode: string): string => {
  const country = countries.find(c => c.code === countryCode);
  return country?.phoneCode || "+1";
};

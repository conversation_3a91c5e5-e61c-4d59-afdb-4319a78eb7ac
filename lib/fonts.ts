/**
 * <PERSON><PERSON>st Font Family Configuration
 * Consistent font weights and typography system for the 10xCFO application
 */

export const fontFamily = {
  thin: 'Geist_100Thin',
  extraLight: 'Geist_200ExtraLight', 
  light: 'Geist_300Light',
  regular: 'Geist_400Regular',
  medium: 'Geist_500Medium',
  semiBold: 'Geist_600SemiBold',
  bold: 'Geist_700Bold',
  extraBold: 'Geist_800ExtraBold',
  black: 'Geist_900Black',
};

export const fontWeights = {
  100: 'font-thin',
  200: 'font-extralight',
  300: 'font-light', 
  400: 'font-normal',
  500: 'font-medium',
  600: 'font-semibold',
  700: 'font-bold',
  800: 'font-extrabold',
  900: 'font-black',
};

export const fontStyles = {
  // Headings
  h1: { 
    className: 'text-h1',
    fontSize: '32px', 
    fontWeight: 700,
    lineHeight: 1.2
  },
  h2: { 
    className: 'text-h2',
    fontSize: '28px', 
    fontWeight: 600,
    lineHeight: 1.3
  },
  h3: { 
    className: 'text-h3',
    fontSize: '24px', 
    fontWeight: 600,
    lineHeight: 1.3
  },
  h4: { 
    className: 'text-h4',
    fontSize: '20px', 
    fontWeight: 500,
    lineHeight: 1.4
  },
  h5: { 
    className: 'text-h5',
    fontSize: '18px', 
    fontWeight: 500,
    lineHeight: 1.4
  },
  h6: { 
    className: 'text-h6',
    fontSize: '16px', 
    fontWeight: 500,
    lineHeight: 1.5
  },
  
  // Body text
  bodyLarge: { 
    className: 'text-body-large',
    fontSize: '18px', 
    fontWeight: 400,
    lineHeight: 1.6
  },
  body: { 
    className: 'text-body',
    fontSize: '16px', 
    fontWeight: 400,
    lineHeight: 1.6
  },
  bodySmall: { 
    className: 'text-body-small',
    fontSize: '14px', 
    fontWeight: 400,
    lineHeight: 1.5
  },
  
  // UI elements
  button: { 
    className: 'text-button',
    fontSize: '16px', 
    fontWeight: 500,
    lineHeight: 1.5
  },
  label: { 
    className: 'text-label',
    fontSize: '14px', 
    fontWeight: 500,
    lineHeight: 1.4
  },
  caption: { 
    className: 'text-caption',
    fontSize: '12px', 
    fontWeight: 400,
    lineHeight: 1.4
  },
};

/**
 * Typography utility function to get consistent font styles
 * @param variant - The typography variant to use
 * @returns Object with className and style properties
 */
export function getTypographyStyle(variant: keyof typeof fontStyles) {
  return fontStyles[variant];
}

/**
 * Font size mapping for consistent sizing across the application
 */
export const fontSizes = {
  xs: '0.75rem',    // 12px
  sm: '0.875rem',   // 14px
  base: '1rem',     // 16px
  lg: '1.125rem',   // 18px
  xl: '1.25rem',    // 20px
  '2xl': '1.5rem',  // 24px
  '3xl': '1.75rem', // 28px
  '4xl': '2rem',    // 32px
  '5xl': '2.25rem', // 36px
  '6xl': '2.5rem',  // 40px
} as const;

/**
 * Line height mapping for consistent spacing
 */
export const lineHeights = {
  none: '1',
  tight: '1.25',
  snug: '1.375',
  normal: '1.5',
  relaxed: '1.625',
  loose: '2',
} as const;

# Migration Guide: From Frontend-Only to Full-Stack Architecture

This guide explains the transition from the original frontend-only TenxCFO application to the new Docker-based full-stack architecture.

## 🔄 What Changed

### Before (Frontend-Only)
- Single Next.js application with static export
- Mock authentication and data
- AWS S3/CloudFront deployment
- No backend or database

### After (Full-Stack)
- Separate frontend and backend applications
- Real authentication with JWT
- PostgreSQL database with Redis caching
- Docker-based development and deployment
- API-driven architecture

## 📁 Directory Structure Changes

### Old Structure
```
tenxcfo-latest/
├── app/                    # Next.js app directory
├── components/            # React components
├── lib/                   # Utilities
├── stores/                # Zustand stores
├── public/               # Static assets
├── package.json          # Dependencies
├── next.config.js        # Next.js config
└── deploy-to-s3.sh      # AWS deployment
```

### New Structure
```
tenxcfo-latest/
├── frontend/              # Next.js application
│   ├── app/              # Moved from root
│   ├── components/       # Moved from root
│   ├── lib/              # Moved from root
│   ├── stores/           # Moved from root
│   ├── public/           # Moved from root
│   ├── package.json      # Frontend dependencies
│   ├── next.config.js    # Updated for API integration
│   └── Dockerfile        # Frontend container
├── backend/               # New Express.js API
│   ├── src/
│   │   ├── routes/       # API endpoints
│   │   ├── middleware/   # Express middleware
│   │   ├── utils/        # Backend utilities
│   │   └── types/        # TypeScript types
│   ├── package.json      # Backend dependencies
│   └── Dockerfile        # Backend container
├── database/              # New database files
│   ├── init.sql          # Schema initialization
│   ├── migrations/       # Schema migrations
│   └── seeds/            # Test data
├── docker-compose.yml     # Multi-service orchestration
├── Makefile              # Development commands
└── setup.sh              # Environment setup
```

## 🔧 Configuration Changes

### Next.js Configuration

**Old `next.config.js`:**
```javascript
const nextConfig = {
  output: 'export',  // Static export for S3
  assetPrefix: 'https://d2tcjdwexogrvt.cloudfront.net',
  // ...
};
```

**New `next.config.js`:**
```javascript
const nextConfig = {
  output: process.env.NODE_ENV === 'production' ? 'standalone' : undefined,
  // API rewrites for backend integration
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL}/api/:path*`,
      },
    ];
  },
  // ...
};
```

### Environment Variables

**New environment variables:**
```bash
# Frontend
NEXT_PUBLIC_API_URL=http://localhost:4000
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Backend
DATABASE_URL=********************************************************/tenxcfo
REDIS_URL=redis://redis:6379
JWT_SECRET=your-secret-key

# Database
POSTGRES_DB=tenxcfo
POSTGRES_USER=tenxcfo_user
POSTGRES_PASSWORD=tenxcfo_password
```

## 🗄️ Data Migration

### From Mock Data to Database

**Old approach (Mock data in stores):**
```typescript
// stores/authStore.ts
const mockUser = {
  id: "dev-user-1",
  email: "<EMAIL>",
  name: "Demo Investor",
  role: "investor",
  // ...
};
```

**New approach (API calls):**
```typescript
// API calls to backend
const response = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email, password }),
});
```

### Authentication Changes

**Old (Mock authentication):**
- Email-based role detection
- No real password validation
- Client-side only

**New (JWT authentication):**
- Real password hashing with bcrypt
- JWT tokens for session management
- Server-side validation

## 🚀 Deployment Changes

### Old Deployment (AWS S3)
```bash
# Build static export
npm run build

# Deploy to S3
./deploy-to-s3.sh
```

### New Deployment (Docker)
```bash
# Development
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

# Production
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up
```

## 🔄 Migration Steps

### For Existing Developers

1. **Backup your current work:**
   ```bash
   git stash  # Save any uncommitted changes
   git branch backup-old-structure  # Create backup branch
   ```

2. **Pull the new structure:**
   ```bash
   git pull origin main
   ```

3. **Set up the new environment:**
   ```bash
   cp .env.example .env
   ./setup.sh
   ```

4. **Verify the migration:**
   ```bash
   make start
   make health
   ```

### For New Developers

Simply follow the [Development Guide](./DEVELOPMENT_GUIDE.md) for a fresh setup.

## 🧪 Testing the Migration

### Verify Frontend Functionality
1. Access http://localhost:3000
2. Test all user roles with test credentials
3. Verify all pages load correctly
4. Check that styling is preserved

### Verify Backend Integration
1. Check API health: http://localhost:4000/api/health
2. Test authentication endpoints
3. Verify database connectivity
4. Test file upload functionality

### Verify Database
1. Connect to database: `make shell-db`
2. Check tables exist: `\dt`
3. Verify seed data: `SELECT * FROM users;`

## 🔍 Troubleshooting Migration Issues

### Frontend Issues

**Problem:** Frontend not loading
```bash
# Check frontend logs
make logs-frontend

# Rebuild frontend
docker-compose build frontend
```

**Problem:** API calls failing
```bash
# Check backend is running
curl http://localhost:4000/api/health

# Check environment variables
make shell-frontend
env | grep NEXT_PUBLIC
```

### Backend Issues

**Problem:** Backend not starting
```bash
# Check backend logs
make logs-backend

# Check dependencies
make shell-backend
npm install
```

**Problem:** Database connection failed
```bash
# Check database status
make status

# Reset database
make reset-db
```

### Database Issues

**Problem:** Tables not created
```bash
# Check database logs
make logs-db

# Manually run init script
make shell-db
\i /docker-entrypoint-initdb.d/init.sql
```

## 📊 Feature Comparison

| Feature | Old (Frontend-Only) | New (Full-Stack) |
|---------|-------------------|------------------|
| Authentication | Mock (client-side) | JWT (server-side) |
| Data Storage | Local state only | PostgreSQL database |
| File Upload | UI only | Real file processing |
| User Management | Mock data | Database-driven |
| Reports | Static mock data | Dynamic generation |
| Deployment | AWS S3 static | Docker containers |
| Development | `npm run dev` | `make start` |
| Scalability | Limited | Highly scalable |

## 🎯 Benefits of the New Architecture

1. **Real Data Persistence**: Data survives browser refreshes and sessions
2. **Scalable Backend**: Can handle multiple users and real workloads
3. **Secure Authentication**: Proper password hashing and JWT tokens
4. **File Processing**: Actual file upload and processing capabilities
5. **Database Relationships**: Proper data modeling with foreign keys
6. **API-First Design**: Frontend and backend can evolve independently
7. **Docker Consistency**: Same environment for all developers
8. **Production Ready**: Suitable for real deployment scenarios

## 🔮 Future Enhancements

The new architecture enables:

- Real-time notifications with WebSockets
- Background job processing
- Third-party integrations
- Microservices architecture
- Cloud deployment (AWS ECS, Kubernetes)
- Automated testing and CI/CD
- Performance monitoring and logging

## 📞 Support

If you encounter issues during migration:

1. Check the [Development Guide](./DEVELOPMENT_GUIDE.md)
2. Review the troubleshooting section above
3. Check logs: `make logs`
4. Try a clean setup: `make clean && make setup`
5. Open an issue with detailed error information

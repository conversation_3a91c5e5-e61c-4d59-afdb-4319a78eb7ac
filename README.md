# TenxCFO Platform

A full-stack data-driven financial intelligence platform that evaluates SMEs, scores their growth potential, and connects them with the right investors for funding and scale.

## 🏗️ Architecture

This is a **Docker-based full-stack application** with:

- **Frontend**: Next.js 15 with TypeScript and Tailwind CSS
- **Backend**: Node.js/Express API with TypeScript
- **Database**: PostgreSQL 15 with Redis for caching
- **Infrastructure**: Docker Compose for development and production

## 📁 Project Structure

```
tenxcfo-latest/
├── frontend/                 # Next.js application
│   ├── app/                 # Next.js app directory
│   ├── components/          # React components
│   ├── lib/                 # Utilities and configurations
│   ├── stores/              # Zustand state management
│   └── Dockerfile
├── backend/                  # Express.js API
│   ├── src/
│   │   ├── controllers/     # Route controllers
│   │   ├── middleware/      # Express middleware
│   │   ├── routes/          # API routes
│   │   ├── services/        # Business logic
│   │   └── utils/           # Utilities
│   └── Dockerfile
├── database/                 # Database scripts
│   ├── init.sql            # Initial schema
│   └── migrations/         # Database migrations
├── docker-compose.yml       # Main Docker configuration
├── docker-compose.dev.yml   # Development overrides
├── docker-compose.prod.yml  # Production overrides
└── Makefile                 # Development commands
```

## 🚀 Quick Start

### Prerequisites

- [Docker](https://docs.docker.com/get-docker/) and Docker Compose
- [Make](https://www.gnu.org/software/make/) (optional, for convenience commands)

### Setup and Run

1. **Clone and setup the environment:**
   ```bash
   git clone <repository-url>
   cd tenxcfo-latest
   
   # Run the setup script
   ./setup.sh
   ```

2. **Or use Make commands:**
   ```bash
   make setup    # Initial setup and start services
   make start    # Start all services
   make logs     # Follow logs
   make stop     # Stop all services
   ```

3. **Or use Docker Compose directly:**
   ```bash
   # Copy environment file
   cp .env.example .env
   
   # Start development environment
   docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
   ```

## 🌐 Access Points

Once running, you can access:

- **Frontend Application**: http://localhost:3000
- **Backend API**: http://localhost:4000
- **API Health Check**: http://localhost:4000/api/health
- **Database**: localhost:5432 (tenxcfo/tenxcfo_user)
- **Redis**: localhost:6379

## 👥 Test Credentials

| Role | Email | Password |
|------|-------|----------|
| **Admin** | `<EMAIL>` | `password123` |
| **SME** | `<EMAIL>` | `password123` |
| **Consultant** | `<EMAIL>` | `password123` |
| **Investor** | `<EMAIL>` | `password123` |

## 🛠️ Development Commands

### Using Make (Recommended)

```bash
make help           # Show all available commands
make start          # Start all services
make stop           # Stop all services
make restart        # Restart all services
make logs           # Follow logs from all services
make logs-backend   # Backend logs only
make logs-frontend  # Frontend logs only
make status         # Show service status
make shell-backend  # Open shell in backend container
make shell-frontend # Open shell in frontend container
make shell-db       # Open PostgreSQL shell
make clean          # Remove all containers and volumes
make reset-db       # Reset database
```

### Using Docker Compose

```bash
# Development
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs -f
docker-compose -f docker-compose.yml -f docker-compose.dev.yml down

# Production
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## 🔧 Development Workflow

1. **Start the development environment:**
   ```bash
   make start
   ```

2. **Make changes to the code:**
   - Frontend changes in `frontend/` will hot-reload automatically
   - Backend changes in `backend/src/` will restart the server automatically

3. **View logs:**
   ```bash
   make logs           # All services
   make logs-frontend  # Frontend only
   make logs-backend   # Backend only
   ```

4. **Access database:**
   ```bash
   make shell-db       # PostgreSQL shell
   ```

5. **Reset database:**
   ```bash
   make reset-db       # Drops and recreates database
   ```

## 🧪 Testing

```bash
# Frontend tests
make test-frontend

# Backend tests
make test-backend

# Linting
make lint-frontend
make lint-backend

# Code formatting
make format
```

## 📦 Production Deployment

1. **Build production images:**
   ```bash
   make prod-build
   ```

2. **Start production services:**
   ```bash
   make prod-start
   ```

3. **With custom environment:**
   ```bash
   cp .env.example .env.production
   # Edit .env.production with production values
   docker-compose -f docker-compose.yml -f docker-compose.prod.yml --env-file .env.production up -d
   ```

## 🔍 Troubleshooting

### Common Issues

1. **Port conflicts:**
   ```bash
   # Check what's using the ports
   lsof -i :3000  # Frontend
   lsof -i :4000  # Backend
   lsof -i :5432  # PostgreSQL
   ```

2. **Database connection issues:**
   ```bash
   # Check database status
   make status
   
   # Reset database
   make reset-db
   ```

3. **Docker issues:**
   ```bash
   # Clean everything and start fresh
   make clean
   make setup
   ```

4. **View service health:**
   ```bash
   make health
   ```

## 📚 Documentation

- **[Development Guide](./DEVELOPMENT_GUIDE.md)** - Detailed setup and development instructions
- **[Migration Guide](./MIGRATION_GUIDE.md)** - Transition from old to new architecture
- **API Documentation** - Available at http://localhost:4000/api/health

### API Endpoints

The backend API provides the following endpoints:

- **Authentication**: `/api/auth/*` (login, register, logout)
- **Users**: `/api/users/*` (user management)
- **Documents**: `/api/documents/*` (file upload and management)
- **Reports**: `/api/reports/*` (report generation and retrieval)
- **Health**: `/api/health` (system health check)

## 🏗️ Architecture Highlights

- **Microservices Ready**: Separate frontend and backend containers
- **Database Driven**: PostgreSQL with proper relationships and migrations
- **Real Authentication**: JWT-based authentication with bcrypt password hashing
- **File Processing**: Actual file upload and storage capabilities
- **Development Friendly**: Hot reloading, easy setup, comprehensive logging
- **Production Ready**: Docker-based deployment with environment configuration

## 🔄 Migration from Old Structure

If you're migrating from the previous frontend-only structure, see the [Migration Guide](./MIGRATION_GUIDE.md) for detailed instructions.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly:
   ```bash
   make test-frontend
   make test-backend
   make lint-frontend
   make lint-backend
   ```
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the [Development Guide](./DEVELOPMENT_GUIDE.md)
- Review the [Migration Guide](./MIGRATION_GUIDE.md) if transitioning
- Check the troubleshooting section in the development guide
- Review the logs: `make logs`
- Open an issue in the repository with detailed information

# TenxCFO Development Makefile
# Provides convenient commands for development workflow

.PHONY: help setup start stop restart logs status clean build test

# Default target
.DEFAULT_GOAL := help

# Docker Compose command detection
DOCKER_COMPOSE := $(shell if docker compose version >/dev/null 2>&1; then echo "docker compose"; else echo "docker-compose"; fi)

# Environment files
DEV_COMPOSE := -f docker-compose.yml -f docker-compose.dev.yml
PROD_COMPOSE := -f docker-compose.yml -f docker-compose.prod.yml

help: ## Show this help message
	@echo "TenxCFO Development Commands"
	@echo "============================"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'
	@echo ""
	@echo "Examples:"
	@echo "  make setup     # Initial setup and start services"
	@echo "  make start     # Start all services"
	@echo "  make logs      # Follow logs from all services"
	@echo "  make stop      # Stop all services"

setup: ## Initial development environment setup
	@echo "🏗️  Setting up TenxCFO development environment..."
	@./setup.sh setup

start: ## Start all development services
	@echo "🚀 Starting development services..."
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) up -d
	@echo "✅ Services started successfully!"
	@make info

stop: ## Stop all services
	@echo "🛑 Stopping all services..."
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) down
	@echo "✅ All services stopped"

restart: ## Restart all services
	@echo "🔄 Restarting all services..."
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) restart
	@make info

logs: ## Show and follow logs from all services
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) logs -f

logs-backend: ## Show backend logs only
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) logs -f backend

logs-frontend: ## Show frontend logs only
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) logs -f frontend

logs-db: ## Show database logs only
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) logs -f postgres

status: ## Show status of all services
	@echo "📊 Service Status:"
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) ps

build: ## Build all Docker images
	@echo "🔨 Building Docker images..."
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) build
	@echo "✅ Images built successfully!"

build-no-cache: ## Build all Docker images without cache
	@echo "🔨 Building Docker images (no cache)..."
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) build --no-cache
	@echo "✅ Images built successfully!"

clean: ## Remove all containers, volumes, and images
	@echo "🧹 Cleaning up development environment..."
	@echo "⚠️  This will remove all containers, volumes, and images!"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ] || exit 1
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) down -v --rmi all
	@docker system prune -f
	@echo "✅ Environment cleaned!"

reset-db: ## Reset database (remove volume and restart)
	@echo "🗄️  Resetting database..."
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) stop postgres
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) rm -f postgres
	@docker volume rm tenxcfo-latest_postgres_data 2>/dev/null || true
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) up -d postgres
	@echo "✅ Database reset complete!"

shell-backend: ## Open shell in backend container
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) exec backend sh

shell-frontend: ## Open shell in frontend container
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) exec frontend sh

shell-db: ## Open PostgreSQL shell
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) exec postgres psql -U tenxcfo_user -d tenxcfo

install-frontend: ## Install frontend dependencies
	@echo "📦 Installing frontend dependencies..."
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) exec frontend npm install

install-backend: ## Install backend dependencies
	@echo "📦 Installing backend dependencies..."
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) exec backend npm install

test-frontend: ## Run frontend tests
	@echo "🧪 Running frontend tests..."
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) exec frontend npm test

test-backend: ## Run backend tests
	@echo "🧪 Running backend tests..."
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) exec backend npm test

lint-frontend: ## Lint frontend code
	@echo "🔍 Linting frontend code..."
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) exec frontend npm run lint

lint-backend: ## Lint backend code
	@echo "🔍 Linting backend code..."
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) exec backend npm run lint

format: ## Format code in both frontend and backend
	@echo "💅 Formatting code..."
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) exec frontend npm run format 2>/dev/null || true
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) exec backend npm run format 2>/dev/null || true

backup-db: ## Backup database
	@echo "💾 Creating database backup..."
	@mkdir -p backups
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) exec postgres pg_dump -U tenxcfo_user tenxcfo > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✅ Database backup created in backups/ directory"

restore-db: ## Restore database from backup (usage: make restore-db BACKUP=filename.sql)
	@if [ -z "$(BACKUP)" ]; then echo "❌ Please specify backup file: make restore-db BACKUP=filename.sql"; exit 1; fi
	@echo "📥 Restoring database from $(BACKUP)..."
	@$(DOCKER_COMPOSE) $(DEV_COMPOSE) exec -T postgres psql -U tenxcfo_user -d tenxcfo < backups/$(BACKUP)
	@echo "✅ Database restored successfully!"

prod-build: ## Build for production
	@echo "🏭 Building for production..."
	@$(DOCKER_COMPOSE) $(PROD_COMPOSE) build
	@echo "✅ Production build complete!"

prod-start: ## Start production services
	@echo "🚀 Starting production services..."
	@$(DOCKER_COMPOSE) $(PROD_COMPOSE) up -d
	@echo "✅ Production services started!"

prod-stop: ## Stop production services
	@echo "🛑 Stopping production services..."
	@$(DOCKER_COMPOSE) $(PROD_COMPOSE) down
	@echo "✅ Production services stopped"

info: ## Show connection information
	@echo ""
	@echo "🌐 TenxCFO Development Environment"
	@echo "================================="
	@echo "📱 Frontend:     http://localhost:3000"
	@echo "🔧 Backend API:  http://localhost:4000"
	@echo "🗄️  Database:    localhost:5432 (tenxcfo/tenxcfo_user)"
	@echo "🔴 Redis:       localhost:6379"
	@echo ""
	@echo "📋 Quick Commands:"
	@echo "   make logs      - Follow all logs"
	@echo "   make status    - Check service status"
	@echo "   make shell-backend - Backend shell"
	@echo "   make shell-frontend - Frontend shell"
	@echo ""

health: ## Check health of all services
	@echo "🏥 Checking service health..."
	@echo "Frontend: $$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000 2>/dev/null || echo "DOWN")"
	@echo "Backend:  $$(curl -s -o /dev/null -w "%{http_code}" http://localhost:4000/api/health 2>/dev/null || echo "DOWN")"
	@echo "Database: $$($(DOCKER_COMPOSE) $(DEV_COMPOSE) exec postgres pg_isready -U tenxcfo_user -d tenxcfo -q && echo "UP" || echo "DOWN")"
	@echo "Redis:    $$($(DOCKER_COMPOSE) $(DEV_COMPOSE) exec redis redis-cli ping 2>/dev/null || echo "DOWN")"
